// 获取画布元素
const canvas = document.getElementById("renderCanvas");
const loadingScreen = document.getElementById("loadingScreen");
const loadingText = document.getElementById("loadingText");

// 获取控制面板元素
const autoRotateBtn = document.getElementById("autoRotateBtn");
const scaleSlider = document.getElementById("scaleSlider");
const envIntensitySlider = document.getElementById("envIntensitySlider");
const metallicSlider = document.getElementById("metallicSlider");
const roughnessSlider = document.getElementById("roughnessSlider");
const resetBtn = document.getElementById("resetBtn");

// 获取部位控制元素
const partsList = document.getElementById("partsList");
const partMetallicSlider = document.getElementById("partMetallicSlider");
const partRoughnessSlider = document.getElementById("partRoughnessSlider");
const partEnvIntensitySlider = document.getElementById("partEnvIntensitySlider");
const resetPartBtn = document.getElementById("resetPartBtn");

// 获取标签页元素
const tabButtons = document.querySelectorAll(".tab-button");
const tabContents = document.querySelectorAll(".tab-content");

// 控制变量
let autoRotate = false; // 默认关闭自动旋转
let modelRoot = null;
let originalPosition = null;
let originalRotation = null;
let originalScale = null;
let scene = null;
let meshes = [];
let selectedMesh = null;
let originalMaterialProperties = new Map(); // 存储原始材质属性

// 初始化Babylon引擎
const engine = new BABYLON.Engine(canvas, true, {
    adaptToDeviceRatio: true,
    powerPreference: "high-performance",
    antialias: false // 关闭抗锯齿以提高性能
});

// 创建场景函数
const createScene = function () {
    // 创建场景
    scene = new BABYLON.Scene(engine);

    // 设置背景色
    scene.clearColor = new BABYLON.Color4(0.2, 0.2, 0.2, 1);

    // 创建并配置相机
    const camera = new BABYLON.ArcRotateCamera("camera", -Math.PI / 2, Math.PI / 2.5, 15, new BABYLON.Vector3(0, 0, 0), scene);
    camera.attachControl(canvas, true);
    camera.wheelPrecision = 50;
    camera.lowerRadiusLimit = 5;
    camera.upperRadiusLimit = 30;

    // 创建光源
    const light1 = new BABYLON.HemisphericLight("light1", new BABYLON.Vector3(1, 1, 0), scene);
    light1.intensity = 0.3; // 降低直接光源的强度，让环境光效果更明显

    const light2 = new BABYLON.DirectionalLight("light2", new BABYLON.Vector3(0, -1, 1), scene);
    light2.intensity = 0.3; // 降低直接光源的强度，让环境光效果更明显

    // 加载HDR环境贴图
    const hdrTexture = new BABYLON.HDRCubeTexture("./HDRguitar.hdr", scene, 192);
    scene.environmentTexture = hdrTexture;

    // 性能优化设置
    scene.blockMaterialDirtyMechanism = true; // 阻止材质频繁更新
    scene.skipPointerMovePicking = true; // 跳过鼠标移动时的拾取检测

    // 启用物理基础渲染（PBR）
    scene.environmentIntensity = parseFloat(envIntensitySlider.value); // 使用滑块的初始值

    // 添加加载进度监听
    const assetsManager = new BABYLON.AssetsManager(scene);

    // 设置加载进度回调
    assetsManager.onProgress = function (remainingCount, totalCount, lastFinishedTask) {
        const progress = Math.round(((totalCount - remainingCount) / totalCount) * 100);
        loadingText.textContent = `加载模型中... ${progress}%`;
    };

    // 加载完成回调
    assetsManager.onFinish = function () {
        loadingScreen.style.display = "none";
    };

    // 加载3D模型
    BABYLON.SceneLoader.ImportMeshAsync("", "./", "tie.glb", scene, function (evt) {
        const progress = Math.round((evt.loaded / evt.total) * 100);
        loadingText.textContent = `加载模型中... ${progress}%`;
    }).then(function (result) {
        // 加载完成后隐藏加载屏幕
        loadingScreen.style.display = "none";

        // 调整模型大小和位置
        meshes = result.meshes;
        modelRoot = meshes[0];

        // 为模型启用环境反射和PBR材质
        convertToPBRMaterials(meshes);

        // 自动调整模型大小以适应视图
        const boundingInfo = calculateBoundingInfo(meshes);
        const size = boundingInfo.max.subtract(boundingInfo.min);
        const maxSize = Math.max(size.x, size.y, size.z);

        // 如果模型太大或太小，进行缩放
        if (maxSize > 10 || maxSize < 1) {
            const scaleFactor = 5 / maxSize;
            modelRoot.scaling = new BABYLON.Vector3(scaleFactor, scaleFactor, scaleFactor);
        }

        // 将模型放置在场景中心
        const center = boundingInfo.min.add(size.scale(0.5));
        modelRoot.position = BABYLON.Vector3.Zero().subtract(center);

        // ------------------- [可在此处设置模型的初始位置和旋转] -------------------
        // 如果需要自定义模型的初始位置，请取消以下代码的注释并修改值
        // modelRoot.position = new BABYLON.Vector3(0, -1, 0); // 示例：将模型沿Y轴下移1个单位

        // 如果需要自定义模型的初始旋转（弧度制），请取消以下代码的注释并修改值
        // modelRoot.rotation = new BABYLON.Vector3(0, Math.PI / 4, 0); // 示例：将模型沿Y轴旋转45度
        // --------------------------------------------------------------------------

        // 保存原始状态，用于重置
        originalPosition = modelRoot.position.clone();
        originalRotation = modelRoot.rotation.clone();
        originalScale = modelRoot.scaling.clone();

        // 移除自动旋转，改用手指滑动控制
        /*
        scene.registerBeforeRender(function () {
            if (autoRotate && modelRoot) {
                modelRoot.rotation.y += 0.003; // 降低旋转速度，减少每帧计算量
            }
        });
        */

        // 添加手指滑动控制旋转
        let previousTouchX = 0;
        let previousTouchY = 0;
        let rotationSpeed = 0;

        // 触摸开始事件
        canvas.addEventListener("touchstart", function (evt) {
            previousTouchX = evt.touches[0].clientX;
            previousTouchY = evt.touches[0].clientY;
            evt.preventDefault();
        });

        // 触摸移动事件
        canvas.addEventListener("touchmove", function (evt) {
            const touch = evt.touches[0];
            const deltaX = touch.clientX - previousTouchX;

            // 根据手指左右滑动方向控制Y轴旋转
            if (modelRoot) {
                modelRoot.rotation.y += deltaX * 0.005; // 控制旋转灵敏度
            }

            previousTouchX = touch.clientX;
            previousTouchY = touch.clientY;

            // 记录旋转速度，用于惯性效果
            rotationSpeed = deltaX * 0.001;

            evt.preventDefault();
        });

        // 触摸结束事件 - 添加惯性效果
        canvas.addEventListener("touchend", function () {
            // 惯性效果 - 设置渐渐减速的旋转
            let inertia = rotationSpeed;

            if (Math.abs(inertia) > 0.001) {
                let inertiaDamping = function () {
                    if (Math.abs(inertia) > 0.0001) {
                        if (modelRoot) {
                            modelRoot.rotation.y += inertia;
                        }
                        inertia *= 0.95; // 每帧减少5%的速度
                        requestAnimationFrame(inertiaDamping);
                    }
                };
                inertiaDamping();
            }
        });

        // 填充部位列表
        populatePartsList(meshes);

        // 设置点击事件以高亮选中的部位
        setupMeshSelection(meshes);
    }).catch(function (error) {
        console.error("加载模型时出错:", error);
        loadingText.textContent = "加载模型失败，请刷新页面重试";
    });

    return scene;
};

// 将模型转换为PBR材质
function convertToPBRMaterials(meshes) {
    meshes.forEach(mesh => {
        if (mesh.material) {
            // 如果模型已有材质，转换为PBR材质
            if (!(mesh.material instanceof BABYLON.PBRMaterial)) {
                const pbr = new BABYLON.PBRMaterial("pbr-" + mesh.name, scene);

                // 尝试从原材质复制属性
                if (mesh.material.albedoTexture) {
                    pbr.albedoTexture = mesh.material.albedoTexture;
                } else if (mesh.material.diffuseTexture) {
                    pbr.albedoTexture = mesh.material.diffuseTexture;
                }

                if (mesh.material.bumpTexture) {
                    pbr.bumpTexture = mesh.material.bumpTexture;
                }

                // 检测是否为金属部分
                let isMetallic = detectMetallicPart(mesh);

                // 设置PBR属性
                if (isMetallic) {
                    // 金属部分设置
                    pbr.metallic = 0.9;  // 高金属度
                    pbr.roughness = 0.1; // 低粗糙度，更光滑
                    pbr.environmentIntensity = 1.2; // 增强环境反射
                } else {
                    // 非金属部分设置
                    pbr.metallic = parseFloat(metallicSlider.value);
                    pbr.roughness = parseFloat(roughnessSlider.value);
                    pbr.environmentIntensity = parseFloat(envIntensitySlider.value);
                }

                // 保存原始材质属性
                originalMaterialProperties.set(mesh.id, {
                    metallic: pbr.metallic,
                    roughness: pbr.roughness,
                    environmentIntensity: pbr.environmentIntensity
                });

                // 应用新材质
                mesh.material = pbr;
            } else {
                // 如果已经是PBR材质，调整其属性
                // 检测是否为金属部分
                let isMetallic = detectMetallicPart(mesh);

                if (isMetallic) {
                    mesh.material.metallic = 0.9;
                    mesh.material.roughness = 0.1;
                    mesh.material.environmentIntensity = 1.2;
                } else {
                    mesh.material.metallic = parseFloat(metallicSlider.value);
                    mesh.material.roughness = parseFloat(roughnessSlider.value);
                    mesh.material.environmentIntensity = parseFloat(envIntensitySlider.value);
                }

                // 保存原始材质属性
                originalMaterialProperties.set(mesh.id, {
                    metallic: mesh.material.metallic,
                    roughness: mesh.material.roughness,
                    environmentIntensity: mesh.material.environmentIntensity
                });
            }
        }
    });

    // 默认将所有材质的金属度提高
    // 这是为了确保即使没有通过名称识别出金属部分，也能提高整体金属质感
    scene.materials.forEach(material => {
        if (material instanceof BABYLON.PBRMaterial) {
            // 如果金属度较低，稍微提高它
            if (material.metallic < 0.5 && !originalMaterialProperties.has(material.id)) {
                material.metallic = parseFloat(metallicSlider.value);
                material.roughness = parseFloat(roughnessSlider.value);

                // 保存原始材质属性
                originalMaterialProperties.set(material.id, {
                    metallic: material.metallic,
                    roughness: material.roughness,
                    environmentIntensity: material.environmentIntensity
                });
            }
        }
    });
}

// 检测是否为金属部分
function detectMetallicPart(mesh) {
    let isMetallic = false;

    // 通过材质名称判断是否为金属
    if (mesh.material && mesh.material.name && (
        mesh.material.name.toLowerCase().includes("metal") ||
        mesh.material.name.toLowerCase().includes("chrome") ||
        mesh.material.name.toLowerCase().includes("steel") ||
        mesh.material.name.toLowerCase().includes("iron") ||
        mesh.material.name.toLowerCase().includes("gold") ||
        mesh.material.name.toLowerCase().includes("silver")
    )) {
        isMetallic = true;
    }

    // 通过网格名称判断是否为金属
    if (mesh.name && (
        mesh.name.toLowerCase().includes("metal") ||
        mesh.name.toLowerCase().includes("chrome") ||
        mesh.name.toLowerCase().includes("steel") ||
        mesh.name.toLowerCase().includes("iron") ||
        mesh.name.toLowerCase().includes("gold") ||
        mesh.name.toLowerCase().includes("silver")
    )) {
        isMetallic = true;
    }

    return isMetallic;
}

// 填充部位列表
function populatePartsList(meshes) {
    // 清空列表
    partsList.innerHTML = '';

    // 过滤掉没有材质的网格和根节点
    const meshesWithMaterial = meshes.filter(mesh => mesh.material && mesh !== modelRoot);

    if (meshesWithMaterial.length === 0) {
        partsList.innerHTML = '<div class="part-item">没有可调整的部位</div>';
        return;
    }

    // 添加每个部位到列表
    meshesWithMaterial.forEach(mesh => {
        const partItem = document.createElement('div');
        partItem.className = 'part-item';
        partItem.dataset.meshId = mesh.id;

        // 使用网格名称或生成一个名称
        let partName = mesh.name || `部位${mesh.id}`;
        if (detectMetallicPart(mesh)) {
            partName += ' (金属)';
        }

        partItem.textContent = partName;

        // 点击事件
        partItem.addEventListener('click', () => {
            // 移除其他选中项的高亮
            document.querySelectorAll('.part-item').forEach(item => {
                item.classList.remove('selected');
            });

            // 高亮当前选中项
            partItem.classList.add('selected');

            // 选择相应的网格
            selectMesh(mesh);
        });

        partsList.appendChild(partItem);
    });
}

// 设置网格选择和高亮
function setupMeshSelection(meshes) {
    // 创建高亮层
    const highlightLayer = new BABYLON.HighlightLayer("highlightLayer", scene);

    // 点击选择网格
    scene.onPointerDown = function (evt, pickResult) {
        if (pickResult.hit && pickResult.pickedMesh) {
            const mesh = pickResult.pickedMesh;

            // 如果点击的是有材质的网格，选择它
            if (mesh.material) {
                selectMesh(mesh);

                // 更新UI中的选中状态
                document.querySelectorAll('.part-item').forEach(item => {
                    item.classList.remove('selected');
                    if (item.dataset.meshId === mesh.id) {
                        item.classList.add('selected');
                    }
                });

                // 切换到部位调整标签页
                switchTab('partsTab');
            }
        }
    };

    // 鼠标悬停高亮
    scene.onPointerMove = function (evt, pickResult) {
        highlightLayer.removeAllMeshes();

        if (pickResult.hit && pickResult.pickedMesh && pickResult.pickedMesh.material) {
            // 不要高亮当前选中的网格
            if (selectedMesh !== pickResult.pickedMesh) {
                highlightLayer.addMesh(pickResult.pickedMesh, BABYLON.Color3.Green());
            }
        }
    };
}

// 选择网格
function selectMesh(mesh) {
    // 清除上一个选中的网格的高亮
    if (selectedMesh) {
        selectedMesh.renderOutline = false;
    }

    // 设置新选中的网格
    selectedMesh = mesh;

    // 高亮显示选中的网格
    if (selectedMesh) {
        selectedMesh.renderOutline = true;
        selectedMesh.outlineColor = BABYLON.Color3.Green();
        selectedMesh.outlineWidth = 0.05;

        // 启用部位控制滑块
        partMetallicSlider.disabled = false;
        partRoughnessSlider.disabled = false;
        partEnvIntensitySlider.disabled = false;
        resetPartBtn.disabled = false;

        // 更新滑块值为当前选中网格的材质属性
        if (selectedMesh.material instanceof BABYLON.PBRMaterial) {
            partMetallicSlider.value = selectedMesh.material.metallic;
            partRoughnessSlider.value = selectedMesh.material.roughness;
            partEnvIntensitySlider.value = selectedMesh.material.environmentIntensity;
        }
    } else {
        // 禁用部位控制滑块
        partMetallicSlider.disabled = true;
        partRoughnessSlider.disabled = true;
        partEnvIntensitySlider.disabled = true;
        resetPartBtn.disabled = true;
    }
}

// 计算所有网格的边界信息
function calculateBoundingInfo(meshes) {
    let min = new BABYLON.Vector3(Number.MAX_VALUE, Number.MAX_VALUE, Number.MAX_VALUE);
    let max = new BABYLON.Vector3(Number.MIN_VALUE, Number.MIN_VALUE, Number.MIN_VALUE);

    for (let i = 0; i < meshes.length; i++) {
        if (meshes[i].getBoundingInfo) {
            const boundingInfo = meshes[i].getBoundingInfo();
            const meshMin = boundingInfo.boundingBox.minimumWorld;
            const meshMax = boundingInfo.boundingBox.maximumWorld;

            min = BABYLON.Vector3.Minimize(min, meshMin);
            max = BABYLON.Vector3.Maximize(max, meshMax);
        }
    }

    return {
        min: min,
        max: max
    };
}

// 更新所有网格的环境光强度
function updateEnvironmentIntensity(value) {
    if (!scene) return;

    // 更新场景环境强度
    scene.environmentIntensity = value;

    // 更新所有PBR材质的环境强度，但保持金属部分的高反射率
    scene.materials.forEach(material => {
        if (material instanceof BABYLON.PBRMaterial) {
            // 检测是否为金属材质
            let isMetallic = material.metallic > 0.7;

            // 如果是金属材质，保持较高的环境反射强度
            if (isMetallic) {
                material.environmentIntensity = Math.max(value, 1.0); // 确保金属部分的环境反射不会太低
            } else {
                material.environmentIntensity = value;
            }
        }
    });
}

// 更新所有网格的金属度
function updateMetallic(value) {
    if (!scene) return;

    // 更新所有非金属PBR材质的金属度
    scene.materials.forEach(material => {
        if (material instanceof BABYLON.PBRMaterial) {
            // 如果不是明确的金属材质，则更新金属度
            if (material.metallic <= 0.7) {
                material.metallic = value;
            }
        }
    });
}

// 更新所有网格的粗糙度
function updateRoughness(value) {
    if (!scene) return;

    // 更新所有非金属PBR材质的粗糙度
    scene.materials.forEach(material => {
        if (material instanceof BABYLON.PBRMaterial) {
            // 如果不是明确的金属材质，则更新粗糙度
            if (material.metallic <= 0.7) {
                material.roughness = value;
            }
        }
    });
}

// 更新选中部位的金属度
function updatePartMetallic(value) {
    if (selectedMesh && selectedMesh.material instanceof BABYLON.PBRMaterial) {
        selectedMesh.material.metallic = value;
    }
}

// 更新选中部位的粗糙度
function updatePartRoughness(value) {
    if (selectedMesh && selectedMesh.material instanceof BABYLON.PBRMaterial) {
        selectedMesh.material.roughness = value;
    }
}

// 更新选中部位的环境反射强度
function updatePartEnvironmentIntensity(value) {
    if (selectedMesh && selectedMesh.material instanceof BABYLON.PBRMaterial) {
        selectedMesh.material.environmentIntensity = value;
    }
}

// 重置选中部位的材质属性
function resetPartMaterial() {
    if (selectedMesh && selectedMesh.material instanceof BABYLON.PBRMaterial) {
        const originalProps = originalMaterialProperties.get(selectedMesh.id);
        if (originalProps) {
            selectedMesh.material.metallic = originalProps.metallic;
            selectedMesh.material.roughness = originalProps.roughness;
            selectedMesh.material.environmentIntensity = originalProps.environmentIntensity;

            // 更新滑块值
            partMetallicSlider.value = originalProps.metallic;
            partRoughnessSlider.value = originalProps.roughness;
            partEnvIntensitySlider.value = originalProps.environmentIntensity;
        }
    }
}

// 切换标签页
function switchTab(tabId) {
    // 移除所有标签页的active类
    tabContents.forEach(content => {
        content.classList.remove('active');
    });

    tabButtons.forEach(button => {
        button.classList.remove('active');
    });

    // 激活选中的标签页
    document.getElementById(tabId).classList.add('active');
    document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');
}

// 创建场景
createScene();

// 渲染循环
engine.runRenderLoop(function () {
    scene.render();
});

// 调整大小事件处理
window.addEventListener("resize", function () {
    engine.resize();
});

// 全局控制面板事件处理
autoRotateBtn.addEventListener("click", function () {
    autoRotate = !autoRotate;
    autoRotateBtn.textContent = autoRotate ? "关闭" : "开启";
});

scaleSlider.addEventListener("input", function () {
    if (modelRoot && originalScale) {
        const scaleFactor = parseFloat(scaleSlider.value);
        modelRoot.scaling = new BABYLON.Vector3(
            originalScale.x * scaleFactor,
            originalScale.y * scaleFactor,
            originalScale.z * scaleFactor
        );
    }
});

envIntensitySlider.addEventListener("input", function () {
    const value = parseFloat(envIntensitySlider.value);
    updateEnvironmentIntensity(value);
});

metallicSlider.addEventListener("input", function () {
    const value = parseFloat(metallicSlider.value);
    updateMetallic(value);
});

roughnessSlider.addEventListener("input", function () {
    const value = parseFloat(roughnessSlider.value);
    updateRoughness(value);
});

resetBtn.addEventListener("click", function () {
    if (modelRoot && originalPosition && originalRotation && originalScale) {
        modelRoot.position = originalPosition.clone();
        modelRoot.rotation = originalRotation.clone();
        modelRoot.scaling = originalScale.clone();
        scaleSlider.value = "1";
        envIntensitySlider.value = "0.8";
        metallicSlider.value = "0.5";
        roughnessSlider.value = "0.4";
        updateEnvironmentIntensity(0.8);
        updateMetallic(0.5);
        updateRoughness(0.4);

        // 重置所有部位的材质
        meshes.forEach(mesh => {
            if (mesh.material instanceof BABYLON.PBRMaterial) {
                const originalProps = originalMaterialProperties.get(mesh.id);
                if (originalProps) {
                    mesh.material.metallic = originalProps.metallic;
                    mesh.material.roughness = originalProps.roughness;
                    mesh.material.environmentIntensity = originalProps.environmentIntensity;
                }
            }
        });

        // 如果有选中的部位，更新其滑块值
        if (selectedMesh && selectedMesh.material instanceof BABYLON.PBRMaterial) {
            partMetallicSlider.value = selectedMesh.material.metallic;
            partRoughnessSlider.value = selectedMesh.material.roughness;
            partEnvIntensitySlider.value = selectedMesh.material.environmentIntensity;
        }

        // 重置相机
        const camera = scene.activeCamera;
        camera.alpha = -Math.PI / 2;
        camera.beta = Math.PI / 2.5;
        camera.radius = 15;
    }
});

// 部位控制面板事件处理
partMetallicSlider.addEventListener("input", function () {
    const value = parseFloat(partMetallicSlider.value);
    updatePartMetallic(value);
});

partRoughnessSlider.addEventListener("input", function () {
    const value = parseFloat(partRoughnessSlider.value);
    updatePartRoughness(value);
});

partEnvIntensitySlider.addEventListener("input", function () {
    const value = parseFloat(partEnvIntensitySlider.value);
    updatePartEnvironmentIntensity(value);
});

resetPartBtn.addEventListener("click", function () {
    resetPartMaterial();
});

// 标签页切换事件处理
tabButtons.forEach(button => {
    button.addEventListener('click', () => {
        const tabId = button.dataset.tab;
        switchTab(tabId);
    });
}); 