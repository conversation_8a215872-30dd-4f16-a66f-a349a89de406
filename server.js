const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = 3000;

const MIME_TYPES = {
    '.html': 'text/html',
    '.js': 'text/javascript',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.wav': 'audio/wav',
    '.mp3': 'audio/mpeg',
    '.mp4': 'video/mp4',
    '.woff': 'application/font-woff',
    '.ttf': 'application/font-ttf',
    '.eot': 'application/vnd.ms-fontobject',
    '.otf': 'application/font-otf',
    '.wasm': 'application/wasm',
    '.glb': 'model/gltf-binary',
    '.gltf': 'model/gltf+json',
    '.exr': 'image/x-exr',
    '.hdr': 'image/vnd.radiance',
    '.env': 'application/octet-stream'
};

// 获取指定目录中的所有3D模型文件
function getModelFiles() {
    try {
        const files = fs.readdirSync('.');
        return files.filter(file => {
            const ext = path.extname(file).toLowerCase();
            return ext === '.glb' || ext === '.gltf';
        });
    } catch (err) {
        console.error('读取模型文件列表时出错:', err);
        return [];
    }
}

const server = http.createServer((req, res) => {
    console.log(`请求: ${req.url}`);

    // 处理API请求
    if (req.url === '/list-models') {
        const models = getModelFiles();
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify(models));
        return;
    }

    // 规范化URL路径
    let filePath = '.' + req.url;
    if (filePath === './') {
        filePath = './index.html';
    }

    // 获取文件扩展名
    const extname = path.extname(filePath).toLowerCase();

    // 默认MIME类型
    let contentType = MIME_TYPES[extname] || 'application/octet-stream';

    // 读取文件
    fs.readFile(filePath, (err, content) => {
        if (err) {
            if (err.code === 'ENOENT') {
                // 文件不存在
                fs.readFile('./404.html', (err, content) => {
                    res.writeHead(404, { 'Content-Type': 'text/html' });
                    res.end(content, 'utf-8');
                });
            } else {
                // 服务器错误
                res.writeHead(500);
                res.end(`服务器错误: ${err.code}`);
            }
        } else {
            // 成功响应
            res.writeHead(200, { 'Content-Type': contentType });
            res.end(content, 'utf-8');
        }
    });
});

server.listen(PORT, () => {
    console.log(`服务器运行在 http://localhost:${PORT}/`);
    console.log('按 Ctrl+C 停止服务器');
    console.log('可用的3D模型:', getModelFiles().join(', '));
    console.log('双模型查看器访问地址: http://localhost:3000/dual_view.html');
    console.log('3D产品册访问地址: http://localhost:3000/home.html');
}); 