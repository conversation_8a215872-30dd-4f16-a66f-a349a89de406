<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视域 - 新能源CCS结构件一站式解决方案</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Montserrat:wght@500;600;700&display=swap"
        rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --primary-light: #3b82f6;
            --accent-color: #8b5cf6;
            --background-color: #ffffff;
            --section-bg-color: #f8fafc;
            --section-bg-alt: #f1f5f9;
            --card-bg-color: #ffffff;
            --text-color: #1e293b;
            --text-secondary: #475569;
            --text-muted-color: #64748b;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --font-family: 'Inter', 'Microsoft YaHei', Arial, sans-serif;
            --heading-font: 'Montserrat', 'Microsoft YaHei', Arial, sans-serif;
        }

        html {
            scroll-behavior: smooth;
            font-size: 16px;
        }

        body {
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
            font-family: var(--font-family);
            background-color: var(--background-color);
            color: var(--text-color);
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            font-family: var(--heading-font);
            font-weight: 600;
        }

        /* 导航栏样式 */
        .navbar {
            background-color: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            box-shadow: var(--shadow-sm);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 5%;
            height: 80px;
            transition: all 0.3s ease;
        }

        .navbar.scrolled {
            height: 70px;
            box-shadow: var(--shadow-md);
        }

        .logo {
            display: flex;
            align-items: center;
        }

        .logo img {
            height: 36px;
            margin-right: 15px;
        }

        .logo h1 {
            font-size: 24px;
            margin: 0;
            color: var(--text-color);
            font-weight: 600;
            letter-spacing: -0.5px;
        }

        .nav-links {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .nav-links li {
            margin: 0 20px;
        }

        .nav-links a {
            text-decoration: none;
            color: var(--text-color);
            font-weight: 500;
            font-size: 15px;
            position: relative;
            padding: 5px 0;
            transition: color 0.3s;
        }

        .nav-links a:after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -5px;
            left: 0;
            background-color: var(--primary-color);
            transition: width 0.3s ease;
        }

        .nav-links a:hover,
        .nav-links a.active {
            color: var(--primary-color);
        }

        .nav-links a:hover:after,
        .nav-links a.active:after {
            width: 100%;
        }

        .language-switch {
            position: relative;
        }

        .lang-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            font-size: 11px;
            font-weight: 600;
            padding: 6px 10px;
            cursor: pointer;
            transition: all 0.3s;
            outline: none;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .lang-btn:hover {
            background: var(--primary-dark);
        }

        .lang-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            min-width: 120px;
            box-shadow: var(--shadow-md);
            z-index: 1000;
            display: none;
        }

        .lang-dropdown.show {
            display: block;
        }

        .lang-option {
            padding: 10px 15px;
            cursor: pointer;
            color: var(--text-color);
            transition: all 0.2s;
            font-size: 14px;
        }

        .lang-option:hover {
            background: var(--section-bg-color);
            color: var(--primary-color);
        }

        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            color: var(--text-color);
            font-size: 24px;
            cursor: pointer;
            padding: 5px;
        }

        /* 自定义没有圆角的汉堡包图标 */
        .hamburger-icon {
            display: block;
            position: relative;
            width: 24px;
            height: 2px;
            background-color: var(--text-color);
        }

        .hamburger-icon::before,
        .hamburger-icon::after {
            content: '';
            position: absolute;
            width: 24px;
            height: 2px;
            background-color: var(--text-color);
            left: 0;
        }

        .hamburger-icon::before {
            top: -8px;
        }

        .hamburger-icon::after {
            bottom: -8px;
        }

        /* 横幅区域 */
        .hero {
            height: 90vh;
            background: linear-gradient(135deg, rgba(37, 99, 235, 0.9), rgba(139, 92, 246, 0.85)), url('https://images.unsplash.com/photo-1620641788421-7a1c342ea42e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1770&q=80');
            background-size: cover;
            background-position: center;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: white;
            padding: 0 20px;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at center, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.3) 100%);
            z-index: 1;
        }

        .hero-content {
            max-width: 900px;
            position: relative;
            z-index: 2;
        }

        .hero h2 {
            font-size: 56px;
            font-weight: 700;
            margin-bottom: 24px;
            line-height: 1.2;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            animation: fadeInDown 1s ease-out;
        }

        .hero p {
            font-size: 20px;
            line-height: 1.6;
            margin-bottom: 40px;
            color: rgba(255, 255, 255, 0.95);
            max-width: 700px;
            margin-left: auto;
            margin-right: auto;
            animation: fadeInUp 1s ease-out 0.3s;
            animation-fill-mode: both;
        }

        .cta-button {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 15px 36px;
            border-radius: 0;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            box-shadow: var(--shadow-md);
            position: relative;
            overflow: hidden;
            animation: fadeInUp 1s ease-out 0.6s;
            animation-fill-mode: both;
        }

        .cta-button::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }

        .cta-button:hover {
            background: var(--primary-dark);
            transform: translateY(-3px);
            box-shadow: var(--shadow-lg);
        }

        .cta-button:hover::after {
            transform: translateX(100%);
        }

        /* Section通用样式 */
        .section {
            padding: 0;
            position: relative;
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.8s ease-out, transform 0.8s ease-out;
        }

        .section.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .section:nth-child(even) {
            background-color: var(--section-bg-color);
        }

        .section-title {
            text-align: center;
            margin-bottom: 40px;
            padding: 60px 8% 20px;
        }

        .section-title h3 {
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 20px;
            position: relative;
            display: inline-block;
            color: var(--text-color);
            letter-spacing: -0.5px;
        }

        .section-title h3:after {
            content: '';
            position: absolute;
            width: 60px;
            height: 3px;
            background: var(--primary-color);
            bottom: -12px;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 3px;
        }

        .section-title p {
            color: var(--text-secondary);
            font-size: 18px;
            max-width: 700px;
            margin: 20px auto 0;
            line-height: 1.6;
        }

        /* About Section */
        #about {
            background-color: var(--section-bg-color);
            position: relative;
            overflow: hidden;
        }

        #about::before {
            content: '';
            position: absolute;
            top: -200px;
            right: -200px;
            width: 400px;
            height: 400px;
            border-radius: 50%;
            background: radial-gradient(var(--primary-light), transparent 70%);
            opacity: 0.1;
        }

        .about-content {
            display: flex;
            align-items: center;
            gap: 80px;
            padding: 0 8% 60px;
            width: 100%;
            box-sizing: border-box;
        }

        .about-text {
            flex: 1;
            line-height: 1.8;
            font-size: 16px;
        }

        .about-text p {
            margin-bottom: 24px;
            color: var(--text-secondary);
        }

        .about-image {
            flex: 1;
            border-radius: 0;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
            position: relative;
        }

        .about-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(37, 99, 235, 0.2), rgba(139, 92, 246, 0.2));
            z-index: 1;
        }

        .about-image img {
            width: 100%;
            display: block;
            transform: scale(1.02);
            transition: transform 0.6s ease;
        }

        .about-image:hover img {
            transform: scale(1.05);
        }

        /* Services Section */
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            padding: 0 8% 60px;
            width: 100%;
            box-sizing: border-box;
        }

        .service-card {
            background-color: var(--card-bg-color);
            border-radius: 0;
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            text-align: center;
            padding: 50px 30px;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            position: relative;
            z-index: 1;
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
            transform: scaleX(0);
            transform-origin: left;
            transition: transform 0.4s ease;
            z-index: -1;
        }

        .service-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow-lg);
            border-color: var(--border-color);
        }

        .service-card:hover::before {
            transform: scaleX(1);
        }

        .service-icon {
            width: 70px;
            height: 70px;
            border-radius: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            background-color: var(--section-bg-color);
            color: var(--primary-color);
            font-size: 28px;
            transition: all 0.3s ease;
        }

        .service-card:hover .service-icon {
            background-color: var(--primary-color);
            color: white;
            transform: rotateY(180deg);
        }

        .service-card h4 {
            margin: 0 0 15px 0;
            font-size: 20px;
            font-weight: 600;
            color: var(--text-color);
        }

        .service-card p {
            color: var(--text-secondary);
            font-size: 15px;
            line-height: 1.6;
        }

        /* Solutions Section */
        .solutions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 30px;
            padding: 0 8% 60px;
            width: 100%;
            box-sizing: border-box;
        }

        /* PC端行业解决方案布局 - 第一行2个，第二行3个 */
        @media (min-width: 992px) {
            .solutions-grid {
                display: grid;
                grid-template-columns: repeat(6, 1fr);
                grid-template-rows: auto auto;
                gap: 10px;
            }

            /* 第一行两个卡片 */
            .solutions-grid .solution-card:nth-child(1) {
                grid-column: 1 / span 3;
                grid-row: 1;
            }

            .solutions-grid .solution-card:nth-child(2) {
                grid-column: 4 / span 3;
                grid-row: 1;
            }

            /* 第二行三个卡片 */
            .solutions-grid .solution-card:nth-child(3) {
                grid-column: 1 / span 2;
                grid-row: 2;
            }

            .solutions-grid .solution-card:nth-child(4) {
                grid-column: 3 / span 2;
                grid-row: 2;
            }

            .solutions-grid .solution-card:nth-child(5) {
                grid-column: 5 / span 2;
                grid-row: 2;
            }
        }

        .solution-card {
            background-color: var(--card-bg-color);
            border-radius: 0;
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
            text-align: center;
            position: relative;
        }

        .solution-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-lg);
        }

        .solution-image-container {
            overflow: hidden;
            height: 280px;
            position: relative;
        }

        .solution-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.4s ease;
        }

        .solution-card:hover .solution-image {
            transform: scale(1.05);
        }

        .solution-info {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            padding: 0;
            background: linear-gradient(to top, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.3) 70%, rgba(0, 0, 0, 0) 100%);
            z-index: 2;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .solution-info h4 {
            margin: 0;
            padding: 0 20px;
            font-size: 22px;
            color: white;
            font-weight: 600;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            width: 100%;
        }

        /* 移动端滑动指示器 - 默认隐藏 */
        .mobile-scroll-indicator {
            display: none;
        }

        /* Products Section */
        #products {
            background-color: var(--section-bg-alt);
            position: relative;
            overflow: hidden;
        }

        #products::after {
            content: '';
            position: absolute;
            bottom: -200px;
            left: -200px;
            width: 400px;
            height: 400px;
            border-radius: 50%;
            background: radial-gradient(var(--accent-color), transparent 70%);
            opacity: 0.05;
            pointer-events: none;
        }

        /* 产品网格样式 */

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 4px;
            margin-top: 40px;
            padding: 0 8% 60px;
            width: 100%;
            box-sizing: border-box;
        }

        /* PC端产品网格增加间距 */
        @media (min-width: 992px) {
            .products-grid {
                gap: 20px;
            }
        }

        .product-card {
            background-color: var(--card-bg-color);
            border-radius: 0;
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 1px solid var(--border-color);
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .model-container {
            position: relative;
            height: 320px;
            /* 增加高度 */
            background-color: var(--section-bg-color);
            overflow: hidden;
            outline: none;
            -webkit-tap-highlight-color: transparent;
        }

        /* 3D图标样式 */
        .model-3d-icon {
            position: absolute;
            top: 10px;
            left: 10px;
            width: 24px;
            height: 24px;
            z-index: 5;
            padding: 4px;
        }

        .model-canvas {
            width: 100%;
            height: 100%;
            display: block;
            transition: transform 0.5s ease;
            outline: none;
            -webkit-tap-highlight-color: transparent;
            user-select: none;
            -webkit-touch-callout: none;
            touch-action: manipulation;
            background-color: #ffffff;
        }

        .product-card:hover .model-canvas {
            transform: scale(1.05);
        }

        /* 图片懒加载样式 */
        .model-canvas {
            transition: opacity 0.3s ease;
        }

        .model-canvas[data-src] {
            opacity: 0.2;
            background-color: #f0f0f0;
        }

        .model-loading {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-color: rgba(255, 255, 255, 0.9);
            color: var(--text-color);
            z-index: 10;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(37, 99, 235, 0.2);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
            margin-bottom: 10px;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        .product-info {
            padding: 10px;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .product-title {
            margin: 0;
            font-size: 15px;
            color: var(--text-color);
            font-weight: 600;
            text-align: center;
        }



        .view-button {
            background-color: transparent;
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
            padding: 8px 18px;
            border-radius: 0;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .view-button:hover {
            background-color: var(--primary-color);
            color: white;
        }

        .filters {
            margin-bottom: 40px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
            width: 100%;
            box-sizing: border-box;
            padding: 0 8% 0 8%;
        }

        .filter-button {
            background-color: var(--card-bg-color);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
            padding: 8px 10px;
            border-radius: 0;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .filter-button:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .filter-button.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .search-box {
            padding: 12px 20px;
            border: 1px solid var(--border-color);
            border-radius: 0;
            width: 250px;
            background-color: var(--card-bg-color);
            color: var(--text-color);
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .search-box::placeholder {
            color: var(--text-muted-color);
        }

        .search-box:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        /* Testimonials Section */
        .testimonial-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 30px;
        }

        .testimonial-card {
            background-color: var(--card-bg-color);
            border-radius: 0;
            padding: 30px;
            border-left: 3px solid var(--primary-color);
            box-shadow: var(--shadow-md);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            height: 100%;
            position: relative;
        }

        .testimonial-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .testimonial-card::before {
            content: '\201C';
            position: absolute;
            top: 20px;
            left: 20px;
            font-size: 60px;
            color: rgba(37, 99, 235, 0.1);
            font-family: Georgia, serif;
            line-height: 1;
        }

        .testimonial-text {
            font-style: italic;
            color: var(--text-secondary);
            margin-bottom: 25px;
            line-height: 1.8;
            font-size: 16px;
            position: relative;
            z-index: 1;
            padding-left: 10px;
        }

        .testimonial-author {
            display: flex;
            align-items: center;
        }

        .testimonial-avatar {
            width: 60px;
            height: 60px;
            border-radius: 0;
            overflow: hidden;
            margin-right: 15px;
            border: 2px solid var(--primary-light);
            box-shadow: var(--shadow-sm);
        }

        .testimonial-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s ease;
        }

        .testimonial-card:hover .testimonial-avatar img {
            transform: scale(1.1);
        }

        .testimonial-info h4 {
            margin: 0;
            font-size: 18px;
            color: var(--text-color);
            font-weight: 600;
        }

        .testimonial-info p {
            margin: 5px 0 0 0;
            color: var(--text-secondary);
            font-size: 14px;
        }

        /* Contact Section */
        #contact {
            background-color: var(--section-bg-alt);
            position: relative;
            overflow: hidden;
        }

        #contact::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 400px;
            height: 400px;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect fill="none" width="100" height="100"/><rect fill="%232563eb" fill-opacity="0.03" x="25" y="25" width="50" height="50" transform="rotate(45, 50, 50)"/></svg>');
            opacity: 0.3;
            z-index: 0;
        }

        .contact-container {
            display: flex;
            gap: 60px;
            position: relative;
            z-index: 1;
            padding: 0 8% 60px;
            width: 100%;
            box-sizing: border-box;
        }

        .contact-info {
            flex: 1;
        }

        .contact-item {
            margin-bottom: 30px;
            display: flex;
            align-items: flex-start;
        }

        .contact-icon {
            width: 48px;
            height: 48px;
            background-color: rgba(37, 99, 235, 0.1);
            border-radius: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            color: var(--primary-color);
            font-size: 20px;
            transition: all 0.3s ease;
            flex-shrink: 0;
        }

        .contact-item:hover .contact-icon {
            background-color: var(--primary-color);
            color: white;
            transform: translateY(-3px);
        }

        .contact-text h4 {
            margin: 0 0 8px 0;
            font-size: 18px;
            color: var(--text-color);
            font-weight: 600;
        }

        .contact-text p {
            margin: 0;
            color: var(--text-secondary);
            line-height: 1.6;
        }

        .contact-form {
            flex: 1;
            background-color: var(--card-bg-color);
            padding: 20px;
            border-radius: 0;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            box-sizing: border-box;
            padding: 14px;
            border: 1px solid var(--border-color);
            border-radius: 0;
            font-size: 15px;
            background-color: var(--background-color);
            color: var(--text-color);
            transition: all 0.3s ease;
            font-family: var(--font-family);
        }

        .form-control::placeholder {
            color: var(--text-muted-color);
        }

        .form-control:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .submit-button {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 14px 30px;
            border-radius: 0;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-block;
            width: 100%;
        }

        .submit-button:hover {
            background: var(--primary-dark);
            transform: translateY(-3px);
            box-shadow: var(--shadow-md);
        }

        /* Footer */
        .footer {
            background-color: var(--text-color);
            color: white;
            padding: 80px 8% 30px;
        }

        .footer-container {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 40px;
            margin-bottom: 60px;
        }

        .footer-col {
            flex: 1;
            min-width: 220px;
        }

        .footer-col h4 {
            color: white;
            margin: 0 0 25px 0;
            font-size: 18px;
            position: relative;
            padding-bottom: 12px;
            font-weight: 600;
        }

        .footer-col h4:after {
            content: '';
            position: absolute;
            width: 40px;
            height: 2px;
            background-color: var(--primary-light);
            bottom: 0;
            left: 0;
        }

        .footer-links {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .footer-links li {
            margin-bottom: 12px;
        }

        .footer-links a {
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 15px;
            display: inline-block;
        }

        .footer-links a:hover {
            color: white;
            transform: translateX(5px);
        }

        .social-icons {
            display: flex;
            gap: 15px;
            margin-top: 25px;
        }

        .social-icons a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 38px;
            height: 38px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 0;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 16px;
        }

        .social-icons a:hover {
            background-color: var(--primary-color);
            color: white;
            transform: translateY(-5px);
        }

        .copyright {
            text-align: center;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding-top: 30px;
            color: rgba(255, 255, 255, 0.6);
            font-size: 14px;
        }

        /* Detail View (Modal) */
        .detail-view {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(15, 23, 42, 0.95);
            z-index: 2000;
            overflow: auto;
            backdrop-filter: blur(10px);
        }

        .detail-container {
            display: flex;
            width: 100%;
            height: 100%;
        }

        .detail-model {
            flex: 2;
            position: relative;
            height: 100%;
            outline: none;
            -webkit-tap-highlight-color: transparent;
        }

        .detail-info {
            flex: 1;
            background-color: var(--background-color);
            color: var(--text-color);
            padding: 50px;
            overflow-y: auto;
            max-width: 450px;
            box-shadow: -5px 0 30px rgba(0, 0, 0, 0.1);
        }

        .detail-info h2 {
            color: var(--primary-color);
            font-size: 28px;
            margin-top: 0;
            margin-bottom: 20px;
            font-weight: 700;
        }

        .detail-info p {
            color: var(--text-secondary);
            line-height: 1.7;
            margin-bottom: 30px;
        }

        .control-group {
            margin-bottom: 25px;
            background: var(--section-bg-color);
            padding: 20px;
            border-radius: 0;
        }

        .control-group label {
            display: block;
            margin-bottom: 12px;
            color: var(--text-secondary);
            font-weight: 500;
            font-size: 15px;
        }

        .control-button {
            background-color: var(--card-bg-color);
            color: var(--text-color);
            border: 1px solid var(--border-color);
            padding: 10px 20px;
            border-radius: 0;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .control-button:hover {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .control-slider {
            width: 100%;
            height: 5px;
            -webkit-appearance: none;
            background: var(--border-color);
            outline: none;
            border-radius: 0;
        }

        .control-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 15px;
            height: 15px;
            border-radius: 0;
            background: var(--primary-color);
            cursor: pointer;
        }

        .control-slider::-moz-range-thumb {
            width: 15px;
            height: 15px;
            border-radius: 0;
            background: var(--primary-color);
            cursor: pointer;
            border: none;
        }

        .close-button {
            position: absolute;
            top: 20px;
            right: 20px;
            background-color: var(--card-bg-color);
            color: var(--text-color);
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 0;
            font-size: 20px;
            cursor: pointer;
            z-index: 110;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
        }

        .close-button:hover {
            background-color: var(--primary-color);
            color: white;
            transform: rotate(90deg);
        }

        /* Keyframe animations */
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive design */
        @media (max-width: 992px) {

            .about-content,
            .contact-container {
                flex-direction: column;
            }

            .detail-container {
                flex-direction: column;
            }

            .detail-info {
                max-width: none;
            }
        }

        @media (max-width: 768px) {
            .navbar {
                padding: 0 20px;
            }

            .mobile-menu-btn {
                display: block;
            }

            .language-switch {
                position: absolute;
                right: 60px;
                top: 50%;
                transform: translateY(-50%);
            }

            .nav-links {
                position: fixed;
                top: 70px;
                left: 0;
                right: 0;
                background-color: rgba(255, 255, 255, 0.98);
                backdrop-filter: blur(10px);
                flex-direction: column;
                align-items: center;
                padding: 20px 0;
                transform: translateY(-150%);
                transition: transform 0.3s ease;
                z-index: 999;
                box-shadow: var(--shadow-md);
            }

            .nav-links.active {
                transform: translateY(0);
            }

            .nav-links li {
                margin: 15px 0;
            }

            .hero h2 {
                font-size: 34px;
            }

            .hero p {
                font-size: 14px;
            }

            .section {
                padding: 0;
            }

            .section-title h3 {
                font-size: 28px;
            }

            .solution-image-container {
                height: 200px;
            }

            /* 移动端滚动提示 */
            .mobile-scroll-indicator {
                display: flex;
                align-items: center;
                justify-content: center;
                margin-bottom: 20px;
                color: var(--text-secondary);
                font-size: 14px;
                gap: 10px;
            }

            .mobile-scroll-indicator .scroll-icon {
                animation: scrollHint 1.5s infinite;
                color: var(--primary-color);
            }

            @keyframes scrollHint {
                0% {
                    transform: translateX(0);
                }

                50% {
                    transform: translateX(3px);
                }

                100% {
                    transform: translateX(0);
                }
            }

            /* 移动端产品网格改为水平滚动 */
            #products3dGrid {
                display: flex;
                grid-template-columns: none;
                flex-wrap: nowrap;
                overflow-x: auto;
                /* scroll-snap-type: x mandatory; */
                -webkit-overflow-scrolling: touch;
            }

            #products3dGrid .product-card {
                min-width: 280px;
                /* 固定最小宽度 */
                width: 80%;
                /* 每个卡片占视口宽度的80% */
                max-width: 320px;
                margin-right: 15px;
                scroll-snap-align: start;
                flex: 0 0 auto;
                height: 420px;
                /* 新增：为移动端卡片设置固定高度 */
            }

            #products3dGrid .product-card:last-child {
                margin-right: 5%;
                /* 最后一个卡片右侧留出空间 */
            }


            /* 隐藏滚动条但保留滚动功能 */
            #products3dGrid::-webkit-scrollbar {
                display: none;
                /* 对于Chrome, Safari和较新的Edge */
            }

            #products3dGrid {
                -ms-overflow-style: none;
                /* 对于IE和Edge */
                scrollbar-width: none;
                /* 对于Firefox */
            }

            /* 普通产品网格保持垂直布局 */
            .services-grid,
            .testimonial-grid {
                grid-template-columns: 1fr;
            }


            /* 全部产品区域在移动端显示为两列 */
            @media (max-width: 768px) {
                #allProductsGrid {
                    grid-template-columns: repeat(2, 1fr);
                    gap: 8px;
                    display: grid !important;
                    /* 强制显示为网格 */
                }

                /* 调整移动端产品卡片样式 */
                #allProductsGrid .product-info {
                    padding: 12px;
                }

                #allProductsGrid .product-title {
                    font-size: 14px;
                    margin-bottom: 8px;
                }

                /* 确保移动端图片容器正确显示 */
                #allProductsGrid .model-container {
                    height: 180px;
                    /* 增加高度 */
                    /* 减小高度以适应移动端 */
                    background-color: #f8f8f8;
                    position: relative;
                }

                /* 确保图片正确填充容器 */
                #allProductsGrid .model-container img {
                    width: 100%;
                    height: 100%;
                    object-fit: contain;
                }
            }

            /* 移动端过滤器按钮水平滚动 */
            .filters {
                flex-wrap: nowrap;
                justify-content: flex-start;
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
                /* scroll-snap-type: x mandatory; */
                padding: 0 5% 10px 5%;
                scrollbar-width: none;
                -ms-overflow-style: none;
            }

            .filters::-webkit-scrollbar {
                display: none;
                /* 对于Chrome, Safari和较新的Edge */
            }

            .filter-button {
                flex: 0 0 auto;
                white-space: nowrap;
                scroll-snap-align: start;
            }

            /* 移动端滑动指示器显示 */
            .mobile-scroll-indicator {
                display: flex;
                align-items: center;
                justify-content: center;
                margin-bottom: 10px;
                color: var(--text-secondary);
                font-size: 12px;
                gap: 5px;
            }

            /* 移动端产品网格样式 */
            .products-grid {
                padding: 0 5% 40px;
            }

            /* 移动端其他容器样式 */
            .about-content,
            .services-grid,
            .solutions-grid,
            .contact-container {
                padding: 0 5% 40px;
            }

            .mobile-scroll-indicator .scroll-icon {
                animation: scrollHint 1.5s infinite;
                color: var(--primary-color);
            }

            @keyframes scrollHint {
                0% {
                    transform: translateX(0);
                }

                50% {
                    transform: translateX(5px);
                }

                100% {
                    transform: translateX(0);
                }
            }
        }
    </style>
    <script src="https://cdn.babylonjs.com/babylon.js"></script>
    <script src="https://cdn.babylonjs.com/loaders/babylonjs.loaders.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css"
        integrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
</head>

<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="logo">
            <img src="logo_wcon.webp" alt="视域" style="height: 40px;">
            <h1></h1>
        </div>
        <ul class="nav-links" id="navLinks">
            <li><a href="#" class="active" data-lang-key="nav_home">首页</a></li>
            <li><a href="#products" data-lang-key="nav_products">产品展示</a></li>
            <li><a href="#about" data-lang-key="nav_about">关于我们</a></li>
            <li><a href="#services" data-lang-key="nav_services">服务内容</a></li>
            <li><a href="#solutions" data-lang-key="nav_solutions">行业方案</a></li>
            <li><a href="#contact" data-lang-key="nav_contact">联系我们</a></li>
        </ul>
        <div class="language-switch">
            <button id="langToggle" class="lang-btn">
                <span id="currentLang">ZH</span>
                <i class="fas fa-caret-down"></i>
            </button>
            <div class="lang-dropdown" id="langDropdown">
                <div class="lang-option" data-lang="zh">ZH</div>
                <div class="lang-option" data-lang="en">EN</div>
            </div>
        </div>
        <button class="mobile-menu-btn" id="mobileMenuBtn">
            <span class="hamburger-icon"></span>
        </button>
    </nav>

    <!-- 横幅区域 -->
    <section class="hero" id="home">
        <div class="hero-content">
            <h2 data-lang-key="hero_title">新能源CCS结构件一站式解决方案</h2>
            <p data-lang-key="hero_description">专注于新能源CCS（Cells Contact System
                集成母排系统）结构件智能制造，为客户提供铜铝母排线、吸塑及注塑线束支架板和信号采集连接的定制解决方案</p>
            <a href="#products" class="cta-button" data-lang-key="hero_cta">浏览产品</a>
        </div>
    </section>

    <!-- 产品展示 -->
    <section id="products" class="section products">
        <div class="section-title">
            <h3 data-lang-key="products_title">3D展示</h3>
            <p data-lang-key="products_subtitle">产品3D展示模块细节</p>
        </div>
        <div class="products-grid" id="products3dGrid">
            <!-- 3D产品卡片将由JS动态生成 -->
        </div>
    </section>

    <!-- 全部产品展示（只用图片） -->
    <section id="allProducts" class="section products">
        <div class="section-title">
            <h3 data-lang-key="all_products_title">全部产品</h3>
            <p data-lang-key="all_products_subtitle">高品质新能源CCS结构件产品系列</p>
        </div>
        <div class="filters">
            <button class="filter-button active" data-filter="all" data-lang-key="filter_all">全部</button>
            <button class="filter-button" data-filter="刚性母线" data-lang-key="filter_rigid_busbar">刚性母线</button>
            <button class="filter-button" data-filter="叠层母排" data-lang-key="filter_laminated_busbar">叠层母排</button>
            <button class="filter-button" data-filter="导电排母线板" data-lang-key="filter_motherboard">导电排母线板</button>
            <button class="filter-button" data-filter="柔性母线" data-lang-key="filter_flexible_busbar">柔性母线</button>
            <button class="filter-button" data-filter="柔性电路刺破压接"
                data-lang-key="filter_flexible_circuit">柔性电路刺破压接</button>
            <button class="filter-button" data-filter="汇流排" data-lang-key="filter_collector_busbar">汇流排</button>
            <button class="filter-button" data-filter="铝基导电排" data-lang-key="filter_aluminum_busbar">铝基导电排</button>
            <button class="filter-button" data-filter="镍基导电排" data-lang-key="filter_nickel_busbar">镍基导电排</button>
        </div>

        <div class="products-grid" id="allProductsGrid">
            <!-- 所有产品卡片（仅图片）将由JS动态生成 -->
        </div>
    </section>

    <!-- 关于我们 -->
    <section id="about" class="section about">
        <div class="section-title">
            <h3 data-lang-key="about_title">关于我们</h3>
            <p data-lang-key="about_subtitle">了解视域如何为新能源行业提供卓越价值</p>
        </div>
        <div class="about-content">
            <div class="about-text">
                <p data-lang-key="about_p1">视域是专业从事新能源CCS结构件的一站式解决方案和智能制造企业，致力于为新能源CCS（Cells Contact System
                    集成母排系统）行业提供高品质的产品和服务。</p>
                <p data-lang-key="about_p2">
                    依托先进的生产工艺和强大的研发团队，我们为客户提供铜铝母排线、吸塑及注塑线束支架板，以及信号采集连接的解决方案和产品，帮助客户实现更高效、更可靠的能源管理系统。</p>
                <p data-lang-key="about_p3">我们的使命是通过持续创新和精益制造，为新能源行业的可持续发展贡献力量，与客户共同成长。</p>
            </div>
            <div class="about-image">
                <img src="https://images.unsplash.com/photo-1581091226033-d5c48150dbaa?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                    alt="关于视域">
            </div>
        </div>
        </div>
    </section>

    <!-- 服务内容 -->
    <section id="services" class="section services">
        <div class="section-title">
            <h3 data-lang-key="services_title">我们的服务</h3>
            <p data-lang-key="services_subtitle">探索我们为新能源行业提供的专业解决方案</p>
        </div>
        <div class="services-grid">
            <div class="service-card">
                <div class="service-icon">
                    <i class="fas fa-bolt"></i>
                </div>
                <h4 data-lang-key="service_busbar_title">铜铝母排系统</h4>
                <p data-lang-key="service_busbar_desc">定制化设计与生产高性能铜铝母排线，满足不同客户的电流传输需求，确保高效率和稳定性</p>
            </div>
            <div class="service-card">
                <div class="service-icon">
                    <i class="fas fa-cogs"></i>
                </div>
                <h4 data-lang-key="service_bracket_title">线束支架板</h4>
                <p data-lang-key="service_bracket_desc">提供高品质吸塑及注塑线束支架板，优化布线系统，提高安装效率和产品可靠性</p>
            </div>
            <div class="service-card">
                <div class="service-icon">
                    <i class="fas fa-microchip"></i>
                </div>
                <h4 data-lang-key="service_signal_title">信号采集连接</h4>
                <p data-lang-key="service_signal_desc">开发先进的信号采集连接解决方案，实现精确的数据监测和系统控制，提升整体性能</p>
            </div>
            <div class="service-card">
                <div class="service-icon">
                    <i class="fas fa-tools"></i>
                </div>
                <h4 data-lang-key="service_manufacturing_title">智能制造服务</h4>
                <p data-lang-key="service_manufacturing_desc">运用智能化生产工艺和严格的质量控制体系，为客户提供高精度、高质量的CCS结构件产品</p>
            </div>
        </div>
        </div>
    </section>

    <!-- 行业解决方案 -->
    <section id="solutions" class="section">
        <div class="section-title">
            <h3 data-lang-key="solutions_title">行业解决方案</h3>
            <p data-lang-key="solutions_subtitle">我们为多个关键行业提供量身定制的CCS结构件解决方案</p>
        </div>
        <div class="solutions-grid">

            <div class="solution-card">
                <div class="solution-image-container">
                    <img src="img_car.jpeg" alt="汽车解决方案" class="solution-image">
                    <div class="solution-info">
                        <h4 data-lang-key="solution_automotive">汽车解决方案</h4>
                    </div>
                </div>
            </div>
            <div class="solution-card">
                <div class="solution-image-container">
                    <img src="img_industry.jpeg" alt="工业解决方案" class="solution-image">
                    <div class="solution-info">
                        <h4 data-lang-key="solution_industrial">工业解决方案</h4>
                    </div>
                </div>
            </div>
            <div class="solution-card">
                <div class="solution-image-container">
                    <img src="img_solar.jpg" alt="新能源解决方案" class="solution-image">
                    <div class="solution-info">
                        <h4 data-lang-key="solution_new_energy">新能源解决方案</h4>
                    </div>
                </div>
            </div>
            <div class="solution-card">
                <div class="solution-image-container">
                    <img src="img_ train.jpeg" alt="轨道交通解决方案" class="solution-image">
                    <div class="solution-info">
                        <h4 data-lang-key="solution_rail">轨道交通解决方案</h4>
                    </div>
                </div>
            </div>
            <div class="solution-card">
                <div class="solution-image-container">
                    <img src="img_medical.jpeg" alt="医疗解决方案" class="solution-image">
                    <div class="solution-info">
                        <h4 data-lang-key="solution_medical">医疗解决方案</h4>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </section>

    <!-- 联系我们 -->
    <section id="contact" class="section contact">
        <div class="section-title">
            <h3 data-lang-key="contact_title">联系我们</h3>
            <p data-lang-key="contact_subtitle">有任何关于新能源CCS结构件的需求，请随时与我们联系</p>
        </div>
        <div class="contact-container">
            <div class="contact-info">
                <div class="contact-item">
                    <div class="contact-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div class="contact-text">
                        <h4 data-lang-key="contact_address_title"> 地址</h4>
                        <p data-lang-key="contact_address">广东省东莞市寮步镇松山湖大道寮步段2号</p>
                    </div>
                </div>
                <div class="contact-item">
                    <div class="contact-icon">
                        <i class="fas fa-phone"></i>
                    </div>
                    <div class="contact-text">
                        <h4 data-lang-key="contact_phone_title">电话</h4>
                        <p>+86 13826130520 <span data-lang-key="contact_person_zhao">赵生</span></p>
                        <p>+86 18825867510 <span data-lang-key="contact_person_huang">黄生</span></p>
                    </div>
                </div>
                <div class="contact-item">
                    <div class="contact-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="contact-text">
                        <h4 data-lang-key="contact_email_title">邮箱</h4>
                        <p><EMAIL></p>
                        <p><EMAIL></p>
                    </div>
                </div>
            </div>
            <div class="contact-form">
                <form id="contactForm">
                    <div class="form-group">
                        <label for="name" class="form-label" data-lang-key="contact_form_name">您的姓名</label>
                        <input type="text" id="name" class="form-control" placeholder="请输入您的姓名"
                            data-lang-key="contact_form_name_placeholder" required>
                    </div>
                    <div class="form-group">
                        <label for="email" class="form-label" data-lang-key="contact_form_email">电子邮箱</label>
                        <input type="email" id="email" class="form-control" placeholder="请输入您的邮箱"
                            data-lang-key="contact_form_email_placeholder" required>
                    </div>
                    <div class="form-group">
                        <label for="phone" class="form-label" data-lang-key="contact_form_phone">电话</label>
                        <input type="tel" id="phone" class="form-control" placeholder="请输入您的电话号码"
                            data-lang-key="contact_form_phone_placeholder">
                    </div>
                    <div class="form-group">
                        <label for="message" class="form-label" data-lang-key="contact_form_message">留言内容</label>
                        <textarea id="message" class="form-control" rows="5" placeholder="请输入您的留言"
                            data-lang-key="contact_form_message_placeholder" required></textarea>
                    </div>
                    <button type="submit" class="submit-button" data-lang-key="contact_form_submit">发送留言</button>
                </form>
            </div>
        </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="footer-container">
            <div class="footer-col">

                <h4 data-lang-key="footer_company">东莞市视域新能源有限公司</h4>
                <div style="margin-bottom: 20px;">
                    <img src="logo_wcon_white.svg" alt="视域" style="height: 40px; margin-bottom: 15px;">
                </div>
                <p style="color: rgba(255, 255, 255, 0.7);" data-lang-key="footer_description">专业新能源CCS结构件的一站式解决方案</p>
            </div>
            <div class="footer-col">
                <h4 data-lang-key="footer_quick_links">快速链接</h4>
                <ul class="footer-links">
                    <li><a href="#" data-lang-key="nav_home">首页</a></li>
                    <li><a href="#products" data-lang-key="nav_products">产品展示</a></li>
                    <li><a href="#about" data-lang-key="nav_about">关于我们</a></li>
                    <li><a href="#services" data-lang-key="nav_services">服务内容</a></li>
                    <li><a href="#solutions" data-lang-key="nav_solutions">行业解决方案</a></li>
                    <li><a href="#contact" data-lang-key="nav_contact">联系我们</a></li>
                </ul>
            </div>
            <div class="footer-col">
                <h4 data-lang-key="footer_products">产品分类</h4>
                <ul class="footer-links">
                    <li><a href="#allProducts" data-lang-key="filter_rigid_busbar">刚性母线</a></li>
                    <li><a href="#allProducts" data-lang-key="filter_laminated_busbar">叠层母排</a></li>
                    <li><a href="#allProducts" data-lang-key="filter_motherboard">导电排母线板</a></li>
                    <li><a href="#allProducts" data-lang-key="filter_flexible_busbar">柔性母线</a></li>
                    <li><a href="#allProducts" data-lang-key="filter_aluminum_busbar">铝基导电排</a></li>
                    <li><a href="#allProducts" data-lang-key="filter_nickel_busbar">镍基导电排</a></li>
                </ul>
            </div>
            <div class="footer-col">
                <h4 data-lang-key="footer_contact">联系我们</h4>
                <ul class="footer-links">
                    <li><a><i class="fas fa-map-marker-alt" style="margin-right: 8px;"></i>
                            <span data-lang-key="contact_address">广东省东莞市寮步镇松山湖大道寮步段2号</span></a></li>
                    <li><a href="tel:+8613826130520"><i class="fas fa-phone" style="margin-right: 8px;"></i> +86
                            13826130520 <span data-lang-key="contact_person_zhao">赵生</span></a></li>
                    <li><a href="tel:+8618825867510"><i class="fas fa-phone" style="margin-right: 8px;"></i> +86
                            18825867510 <span data-lang-key="contact_person_huang">黄生</span></a></li>
                    <li><a href="mailto:<EMAIL>"><i class="fas fa-envelope" style="margin-right: 8px;"></i>
                            <EMAIL></a></li>
                    <li><a href="mailto:<EMAIL>"><i class="fas fa-envelope"
                                style="margin-right: 8px;"></i> <EMAIL></a></li>
                </ul>
            </div>
        </div>
        <div class="copyright">
            <p data-lang-key="footer_copyright">&copy; 2025 东莞市视域新能源有限公司 版权所有</p>
        </div>
    </footer>

    <!-- 3D模型详情弹窗 -->
    <div class="detail-view" id="detailView">
        <button class="close-button" id="closeDetail">&times;</button>
        <div class="detail-container">
            <div class="detail-model">
                <img src="icon_3d.svg" alt="3D" class="model-3d-icon">
                <canvas id="detailCanvas" class="model-canvas"></canvas>
                <div class="model-loading" id="detailLoading">
                    <div class="spinner"></div>
                    <div data-lang-key="loading_text">加载模型中...</div>
                </div>
            </div>
            <div class="detail-info" id="detailInfo">
                <h2 id="detailTitle">产品名称</h2>
                <p id="detailDescription">产品描述...</p>

                <div class="control-group">
                    <label for="autoRotateBtn" data-lang-key="detail_auto_rotate">自动旋转</label>
                    <button id="autoRotateBtn" class="control-button" data-lang-key="btn_on">开启</button>
                </div>
                <div class="control-group">
                    <label for="scaleSlider" data-lang-key="detail_scale">模型大小</label>
                    <input type="range" id="scaleSlider" class="control-slider" min="0.5" max="2" step="0.1" value="1">
                </div>
                <div class="control-group">
                    <label for="envIntensitySlider" data-lang-key="detail_env_intensity">环境光强度</label>
                    <input type="range" id="envIntensitySlider" class="control-slider" min="0" max="2" step="0.1"
                        value="0.8">
                </div>
                <div class="control-group">
                    <label for="metallicSlider" data-lang-key="detail_metallic">全局金属质感</label>
                    <input type="range" id="metallicSlider" class="control-slider" min="0" max="1" step="0.05"
                        value="0.5">
                </div>
                <div class="control-group">
                    <label for="roughnessSlider" data-lang-key="detail_roughness">全局表面光滑度</label>
                    <input type="range" id="roughnessSlider" class="control-slider" min="0" max="1" step="0.05"
                        value="0.4">
                </div>
                <div class="control-group">
                    <label for="resetBtn" data-lang-key="detail_reset">重置视图</label>
                    <button id="resetBtn" class="control-button" data-lang-key="btn_reset">重置</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // 导航菜单响应式处理
            const mobileMenuBtn = document.getElementById('mobileMenuBtn');
            const navLinks = document.getElementById('navLinks');
            const navAnchors = navLinks.querySelectorAll('a');

            if (mobileMenuBtn) {
                mobileMenuBtn.addEventListener('click', function () {
                    navLinks.classList.toggle('active');
                });
            }

            // 点击链接后关闭移动菜单
            navAnchors.forEach(link => {
                link.addEventListener('click', () => {
                    if (navLinks.classList.contains('active')) {
                        navLinks.classList.remove('active');
                    }
                });
            });

            // 导航链接激活状态
            const sections = document.querySelectorAll('section[id]');
            const navLi = document.querySelectorAll('.nav-links li a');

            window.addEventListener('scroll', () => {
                let current = 'home'; // Default to home
                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    if (pageYOffset >= sectionTop - 75) {
                        current = section.getAttribute('id');
                    }
                });

                navLi.forEach(a => {
                    a.classList.remove('active');
                    let href = a.getAttribute('href');
                    // For href="#" check if current is 'home', for others check matching id
                    if ((href === '#' && current === 'home') || (href.substring(1) === current)) {
                        a.classList.add('active');
                    }
                });
            });

            // 滚动显示动画
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                    }
                });
            }, {
                threshold: 0.1
            });

            document.querySelectorAll('.section').forEach(section => {
                observer.observe(section);
            });
        });
    </script>

    <script src="home.js"></script>
    <script src="debug.js"></script>

    <!-- 中英文切换功能 -->
    <script>
        // 语言数据
        const languages = {
            'zh': {
                'nav_home': '首页',
                'nav_products': '产品展示',
                'nav_about': '关于我们',
                'nav_services': '服务内容',
                'nav_solutions': '行业方案',
                'nav_contact': '联系我们',
                'hero_title': '新能源CCS结构件一站式解决方案',
                'hero_description': '专注于新能源CCS（Cells Contact System 集成母排系统）结构件智能制造，为客户提供铜铝母排线、吸塑及注塑线束支架板和信号采集连接的定制解决方案',
                'hero_cta': '浏览产品',
                'products_title': '3D展示',
                'products_subtitle': '产品3D展示模块细节',
                'all_products_title': '全部产品',
                'all_products_subtitle': '高品质新能源CCS结构件产品系列',
                'filter_all': '全部',
                'filter_rigid_busbar': '刚性母线',
                'filter_laminated_busbar': '叠层母排',
                'filter_motherboard': '导电排母线板',
                'filter_flexible_busbar': '柔性母线',
                'filter_flexible_circuit': '柔性电路刺破压接',
                'filter_collector_busbar': '汇流排',
                'filter_aluminum_busbar': '铝基导电排',
                'filter_nickel_busbar': '镍基导电排',
                'search_placeholder': '搜索产品...',
                'about_title': '关于我们',
                'about_subtitle': '了解视域如何为新能源行业提供卓越价值',
                'about_p1': '视域是专业从事新能源CCS结构件的一站式解决方案和智能制造企业，致力于为新能源CCS（Cells Contact System 集成母排系统）行业提供高品质的产品和服务。',
                'about_p2': '依托先进的生产工艺和强大的研发团队，我们为客户提供铜铝母排线、吸塑及注塑线束支架板，以及信号采集连接的解决方案和产品，帮助客户实现更高效、更可靠的能源管理系统。',
                'about_p3': '我们的使命是通过持续创新和精益制造，为新能源行业的可持续发展贡献力量，与客户共同成长。',
                'services_title': '我们的服务',
                'services_subtitle': '探索我们为新能源行业提供的专业解决方案',
                'service_busbar_title': '铜铝母排系统',
                'service_busbar_desc': '定制化设计与生产高性能铜铝母排线，满足不同客户的电流传输需求，确保高效率和稳定性',
                'service_bracket_title': '线束支架板',
                'service_bracket_desc': '提供高品质吸塑及注塑线束支架板，优化布线系统，提高安装效率和产品可靠性',
                'service_signal_title': '信号采集连接',
                'service_signal_desc': '开发先进的信号采集连接解决方案，实现精确的数据监测和系统控制，提升整体性能',
                'service_manufacturing_title': '智能制造服务',
                'service_manufacturing_desc': '运用智能化生产工艺和严格的质量控制体系，为客户提供高精度、高质量的CCS结构件产品',
                'view_details': '查看详情',
                'loading_text': '加载模型中...',
                'solutions_title': '行业解决方案',
                'solutions_subtitle': '我们为多个关键行业提供量身定制的CCS结构件解决方案',
                'solution_new_energy': '新能源解决方案',
                'solution_automotive': '汽车解决方案',
                'solution_industrial': '工业解决方案',
                'solution_rail': '轨道交通解决方案',
                'solution_medical': '医疗解决方案',
                'contact_title': '联系我们',
                'contact_subtitle': '有任何关于新能源CCS结构件的需求，请随时与我们联系',
                'contact_address_title': '地址',
                'contact_address': '广东省东莞市寮步镇松山湖大道寮步段2号',
                'contact_phone_title': '电话',
                'contact_person_zhao': '赵生',
                'contact_person_huang': '黄生',
                'contact_email_title': '邮箱',
                'contact_form_name': '您的姓名',
                'contact_form_email': '电子邮箱',
                'contact_form_phone': '电话',
                'contact_form_message': '留言内容',
                'contact_form_submit': '发送留言',
                'contact_form_name_placeholder': '请输入您的姓名',
                'contact_form_email_placeholder': '请输入您的邮箱',
                'contact_form_phone_placeholder': '请输入您的电话号码',
                'contact_form_message_placeholder': '请输入您的留言',
                'footer_company': '东莞市视域新能源有限公司',
                'footer_description': '专业新能源CCS结构件的一站式解决方案',
                'footer_quick_links': '快速链接',
                'footer_products': '产品分类',
                'footer_contact': '联系我们',
                'footer_copyright': '© 2025 东莞市视域新能源有限公司 版权所有',
                'detail_auto_rotate': '自动旋转',
                'detail_scale': '模型大小',
                'detail_env_intensity': '环境光强度',
                'detail_metallic': '全局金属质感',
                'detail_roughness': '全局表面光滑度',
                'detail_reset': '重置视图',
                'btn_on': '开启',
                'btn_off': '关闭',
                'btn_reset': '重置',
                'scroll_more': '滑动查看更多',
                'loading_error': 'Failed to load model. Please try again.'
            },
            'en': {
                'nav_home': 'Home',
                'nav_products': 'Products',
                'nav_about': 'About Us',
                'nav_services': 'Services',
                'nav_solutions': 'Solutions',
                'nav_contact': 'Contact',
                'hero_title': 'One-Stop Solution for New Energy CCS Components',
                'hero_description': 'Specialized in intelligent manufacturing of CCS (Cells Contact System) components, providing customized solutions for copper/aluminum busbars, thermoformed/injection molded wire harness brackets, and signal acquisition connections.',
                'hero_cta': 'View Products',
                'products_title': '3D Display',
                'products_subtitle': 'Product 3D display module details',
                'all_products_title': 'All Products',
                'all_products_subtitle': 'High-quality new energy CCS component series',
                'filter_all': 'All',
                'filter_rigid_busbar': 'Rigid Busbar',
                'filter_laminated_busbar': 'Laminated Busbar',
                'filter_motherboard': 'Conductive Busbar Board',
                'filter_flexible_busbar': 'Flexible Busbar',
                'filter_flexible_circuit': 'Flexible Circuit IDC',
                'filter_collector_busbar': 'Collector Busbar',
                'filter_aluminum_busbar': 'Aluminum Busbar',
                'filter_nickel_busbar': 'Nickel Busbar',
                'search_placeholder': 'Search products...',
                'about_title': 'About Us',
                'about_subtitle': 'Discover how View provides exceptional value to the new energy industry',
                'about_p1': 'View is a professional one-stop solution provider and intelligent manufacturer of new energy CCS components, dedicated to providing high-quality products and services for the CCS (Cells Contact System) industry.',
                'about_p2': 'Relying on advanced production technology and a strong R&D team, we provide copper/aluminum busbar lines, thermoformed and injection molded wire harness brackets, and signal acquisition connection solutions and products, helping customers achieve more efficient and reliable energy management systems.',
                'about_p3': 'Our mission is to contribute to the sustainable development of the new energy industry through continuous innovation and lean manufacturing, growing together with our customers.',
                'services_title': 'Our Services',
                'services_subtitle': 'Explore our professional solutions for the new energy industry',
                'service_busbar_title': 'Copper & Aluminum Busbar Systems',
                'service_busbar_desc': 'Custom design and production of high-performance copper and aluminum busbars to meet different current transmission requirements, ensuring efficiency and stability',
                'service_bracket_title': 'Wire Harness Brackets',
                'service_bracket_desc': 'Provide high-quality thermoformed and injection molded wire harness brackets, optimizing wiring systems, improving installation efficiency and product reliability',
                'service_signal_title': 'Signal Acquisition Connection',
                'service_signal_desc': 'Development of advanced signal acquisition connection solutions, enabling precise data monitoring and system control to enhance overall performance',
                'service_manufacturing_title': 'Intelligent Manufacturing',
                'service_manufacturing_desc': 'Using intelligent production processes and strict quality control systems to provide customers with high-precision, high-quality CCS structural components',
                'view_details': 'View Details',
                'loading_text': 'Loading Model...',
                'solutions_title': 'Industry Solutions',
                'solutions_subtitle': 'We provide tailored CCS component solutions for multiple key industries',
                'solution_new_energy': 'New Energy Solution',
                'solution_automotive': 'Automotive Solution',
                'solution_industrial': 'Industrial Solution',
                'solution_rail': 'Rail Transit Solution',
                'solution_medical': 'Medical Solution',
                'contact_title': 'Contact Us',
                'contact_subtitle': 'For any needs regarding new energy CCS components, please feel free to contact us',
                'contact_address_title': 'Address',
                'contact_address': 'No. 2, Liaobu Section, Songshan Lake Avenue, Liaobu Town, Dongguan City, Guangdong Province',
                'contact_phone_title': 'Phone',
                'contact_person_zhao': 'Mr. Zhao',
                'contact_person_huang': 'Mr. Huang',
                'contact_email_title': 'Email',
                'contact_form_name': 'Your Name',
                'contact_form_email': 'Email',
                'contact_form_phone': 'Phone',
                'contact_form_message': 'Message',
                'contact_form_submit': 'Send Message',
                'contact_form_name_placeholder': 'Enter your name',
                'contact_form_email_placeholder': 'Enter your email',
                'contact_form_phone_placeholder': 'Enter your phone number',
                'contact_form_message_placeholder': 'Enter your message',
                'footer_company': 'Dongguan View New Energy Co., Ltd.',
                'footer_description': 'Professional one-stop solution for new energy CCS components',
                'footer_quick_links': 'Quick Links',
                'footer_products': 'Product Categories',
                'footer_contact': 'Contact Us',
                'footer_copyright': '© 2025 Dongguan View New Energy Co., Ltd. All rights reserved',
                'detail_auto_rotate': 'Auto Rotate',
                'detail_scale': 'Model Scale',
                'detail_env_intensity': 'Environment Light Intensity',
                'detail_metallic': 'Global Metallic',
                'detail_roughness': 'Global Roughness',
                'detail_reset': 'Reset View',
                'btn_on': 'On',
                'btn_off': 'Off',
                'btn_reset': 'Reset',
                'scroll_more': 'Scroll More',
                'loading_error': 'Failed to load model. Please try again.'
            }
        };

        // 当前语言
        let currentLang = localStorage.getItem('language') || 'zh';

        // 初始化语言设置
        document.addEventListener('DOMContentLoaded', function () {
            // 设置语言切换按钮状态
            updateLanguageToggle();

            // 应用已保存的语言设置
            applyLanguage(currentLang);

            // 添加语言切换点击事件
            const langToggleBtn = document.getElementById('langToggle');
            const langDropdown = document.getElementById('langDropdown');
            const langOptions = document.querySelectorAll('.lang-option');

            // 点击按钮显示/隐藏下拉菜单
            langToggleBtn.addEventListener('click', function (e) {
                e.stopPropagation();
                langDropdown.classList.toggle('show');
            });

            // 点击选项切换语言
            langOptions.forEach(option => {
                option.addEventListener('click', function (e) {
                    e.stopPropagation();
                    const selectedLang = this.getAttribute('data-lang');
                    if (selectedLang !== currentLang) {
                        currentLang = selectedLang;
                        localStorage.setItem('language', currentLang);
                        applyLanguage(currentLang);
                        updateLanguageToggle();
                    }
                    langDropdown.classList.remove('show');
                });
            });

            // 点击页面其他地方关闭下拉菜单
            document.addEventListener('click', function () {
                langDropdown.classList.remove('show');
            });
        });

        // 更新语言切换按钮的文本
        function updateLanguageToggle() {
            const currentLangSpan = document.getElementById('currentLang');
            if (currentLang === 'zh') {
                currentLangSpan.textContent = 'CH';
            } else {
                currentLangSpan.textContent = 'EN';
            }
        }

        // 应用语言翻译
        function applyLanguage(lang) {
            // 获取所有带有data-lang-key属性的元素
            const elements = document.querySelectorAll('[data-lang-key]');

            // 遍历这些元素并应用翻译
            elements.forEach(element => {
                const key = element.getAttribute('data-lang-key');
                if (languages[lang] && languages[lang][key]) {
                    // 检查元素类型并相应地设置文本
                    if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                        if (element.placeholder) {
                            element.placeholder = languages[lang][key];
                        } else {
                            element.value = languages[lang][key];
                        }
                    } else {
                        element.textContent = languages[lang][key];
                    }
                }
            });

            // 特殊处理：更新文档标题
            if (lang === 'en') {
                document.title = "View - One-Stop Solution for New Energy CCS Components";
            } else {
                document.title = "视域 - 新能源CCS结构件一站式解决方案";
            }

            // 特殊处理：更新3D产品标题
            // 检查全局产品数据是否存在
            if (typeof productData !== 'undefined' && productData) {
                updateProductTitles(lang);
            }

            // 触发语言变化事件，让动态创建的元素也能更新
            const event = new CustomEvent('languageChanged', { detail: { language: lang } });
            window.dispatchEvent(event);
        }

        // 更新产品标题函数
        function updateProductTitles(lang) {
            // 更新3D产品卡片标题
            const productCards = document.querySelectorAll('.product-card');
            productCards.forEach(card => {
                const productId = card.getAttribute('data-product-id');
                if (productId) {
                    const titleElement = card.querySelector('.product-title');
                    if (titleElement) {
                        const product = productData.find(p => p.id.toString() === productId);
                        if (product && product.title && product.title[lang]) {
                            titleElement.textContent = product.title[lang];
                        }
                    }
                }
            });

            // 更新详情页中的产品标题和描述
            const detailTitle = document.getElementById('detailTitle');
            const detailDescription = document.getElementById('detailDescription');
            const detailView = document.getElementById('detailView');

            if (detailView && detailView.style.display === 'block' && detailTitle) {
                const currentProductId = detailView.getAttribute('data-current-product');
                if (currentProductId) {
                    const product = productData.find(p => p.id.toString() === currentProductId);
                    if (product) {
                        if (product.title && product.title[lang]) {
                            detailTitle.textContent = product.title[lang];
                        }
                        if (detailDescription && product.description && product.description[lang]) {
                            detailDescription.textContent = product.description[lang];
                        }
                    }
                }
            }
        }
    </script>
</body>

</html>