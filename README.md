# Babylon.js 3D模型查看器

这是一个使用Babylon.js创建的简单3D模型查看器，用于加载和显示GLB格式的3D模型。

## 功能特点

- 加载并显示GLB格式的3D模型
- 使用HDR环境贴图提供逼真的光照和反射效果
- 增强金属部分的金属质感和反射效果
- 支持对模型不同部位进行单独材质调整
- 自动调整模型大小和位置
- 加载进度显示
- 交互式相机控制（旋转、缩放、平移）
- 模型控制面板：
  - 全局控制：调整整个模型的属性
  - 部位调整：单独调整模型各个部位的材质属性

## 使用方法

1. 确保您的模型文件命名为`tie.glb`并放置在网站根目录下
2. 确保环境贴图文件`HDRguitar.env`也放置在网站根目录下
3. 使用现代浏览器（如Chrome、Firefox、Edge等）打开`index.html`文件

### 控制说明

#### 全局控制
- **鼠标左键拖动**：旋转相机视角
- **鼠标滚轮**：缩放视图
- **鼠标右键拖动**：平移相机
- **自动旋转按钮**：开启/关闭模型自动旋转
- **模型大小滑块**：调整模型的显示大小
- **环境光强度滑块**：调整环境光照和反射的强度
- **金属质感滑块**：调整模型的金属度（值越高，金属感越强）
- **表面光滑度滑块**：调整模型表面的光滑度（值越低，反射越清晰）
- **重置按钮**：将模型和相机恢复到初始状态

#### 部位调整
- **部位选择**：点击列表中的部位或直接点击模型上的部位进行选择
- **部位金属度**：调整所选部位的金属度
- **部位光滑度**：调整所选部位的光滑度
- **部位环境反射强度**：调整所选部位的环境反射强度
- **重置部位按钮**：将所选部位恢复到初始状态

## 技术说明

- 使用Babylon.js 5.0+
- 使用PBR（物理基础渲染）材质实现逼真的光照效果
- 使用HDR环境贴图提供环境光照和反射
- 智能识别模型中的金属部分，增强其金属质感
- 提供金属度和光滑度控制，实现不同材质效果
- 支持对模型各个部位进行单独材质调整
- 交互式部位选择和高亮显示
- 纯JavaScript实现，无需其他依赖
- 响应式设计，适应不同屏幕大小

## 如何替换模型

如果您想加载其他GLB模型，请将您的模型文件重命名为`tie.glb`并替换原有文件，或者修改`app.js`文件中的模型文件名：

```javascript
// 找到这一行
BABYLON.SceneLoader.ImportMeshAsync("", "./", "tie.glb", scene, function(evt) {
// 将"tie.glb"替换为您的模型文件名
```

## 如何替换环境贴图

如果您想使用其他环境贴图，请将您的环境贴图文件重命名为`HDRguitar.env`并替换原有文件，或者修改`app.js`文件中的环境贴图文件名：

```javascript
// 找到这一行
const hdrTexture = new BABYLON.CubeTexture.CreateFromPrefilteredData("./HDRguitar.env", scene);
// 将"HDRguitar.env"替换为您的环境贴图文件名
```

## 金属质感调整说明

本应用会自动尝试识别模型中的金属部分（通过名称包含"metal"、"chrome"、"steel"等关键词），并为其应用更高的金属度和更低的粗糙度，以增强金属质感。您也可以通过控制面板中的滑块手动调整这些参数：

- **金属度(Metallic)**: 控制表面的金属特性，值为0表示非金属材质，值为1表示纯金属材质
- **光滑度(Smoothness)**: 控制表面的粗糙程度，值为0表示极粗糙表面，值为1表示镜面光滑

## 部位调整功能使用说明

1. 点击顶部的"部位调整"标签切换到部位调整模式
2. 在部位列表中选择要调整的部位，或直接点击3D模型上的部位
3. 选中的部位会高亮显示，并且部位控制滑块会显示该部位当前的材质属性
4. 使用滑块调整所选部位的金属度、光滑度和环境反射强度
5. 点击"重置部位"按钮可以将所选部位恢复到初始状态
6. 点击"全局控制"标签可以返回全局控制模式 