// 产品数据（实际项目中可以从API获取）
window.productData = [
    {
        id: 1,
        title: {
            zh: "导电排注塑/吸塑母线版",
            en: "Busbar Injection/Thermoforming Motherboard"
        },
        description: {
            zh: "导电排注塑/吸塑母线版，用于高效电力传输",
            en: "Busbar injection/thermoforming plastic motherboard for efficient power transmission"
        },
        type: "3d",
        model: "1.glb",
        image: "logo_wcon.webp", // 产品图片 - 用于"全部产品"展示
        thumbnail: "1.glb", // 实际上我们会用这个模型来实时渲染缩略图
        tags: ["popular", "new"],
        cameraPosition: { alpha: Math.PI / 4, beta: Math.PI / 1.5, radius: 8 }
    },
    {
        id: 2,
        title: {
            zh: "镍基导电排",
            en: "Nickel Based Busbar"
        },
        description: {
            zh: "镍基导电排，用于高温环境下的稳定导电",
            en: "Nickel based busbar for stable conductivity in high temperature environments"
        },
        type: "3d",
        model: "2.glb",
        image: "logo_wcon.webp", // 产品图片
        thumbnail: "2.glb",
        tags: ["popular"],
        cameraPosition: { alpha: Math.PI / 4, beta: Math.PI / 1.5, radius: 8 }
    },
    {
        id: 3,
        title: {
            zh: "镍基导电排",
            en: "Nickel Based Busbar"
        },
        description: {
            zh: "镍基导电排，用于高温环境下的稳定导电",
            en: "Nickel based busbar for stable conductivity in high temperature environments"
        },
        type: "3d",
        model: "3.glb",
        image: "logo_wcon.webp", // 产品图片
        thumbnail: "3.glb",
        tags: ["popular"],
        cameraPosition: { alpha: Math.PI / 4, beta: Math.PI / 1.5, radius: 8 }
    },
    {
        id: 4,
        title: {
            zh: "镍基导电排",
            en: "Nickel Based Busbar"
        },
        description: {
            zh: "镍基导电排，用于高温环境下的稳定导电",
            en: "Nickel based busbar for stable conductivity in high temperature environments"
        },
        type: "3d",
        model: "4.glb",
        image: "logo_wcon.webp", // 产品图片
        thumbnail: "4.glb",
        tags: ["popular"],
        cameraPosition: { alpha: Math.PI / 4, beta: Math.PI / 1.5, radius: 8 }
    },
    {
        id: 5,
        title: {
            zh: "镍基导电排",
            en: "Nickel Based Busbar"
        },
        description: {
            zh: "镍基导电排，用于高温环境下的稳定导电",
            en: "Nickel based busbar for stable conductivity in high temperature environments"
        },
        type: "3d",
        model: "5.glb",
        image: "logo_wcon.webp", // 产品图片
        thumbnail: "5.glb",
        tags: ["popular"],
        cameraPosition: { alpha: Math.PI / 4, beta: Math.PI / 1.5, radius: 8 }
    },
    {
        id: 6,
        title: {
            zh: "镍基导电排",
            en: "Nickel Based Busbar"
        },
        description: {
            zh: "镍基导电排，用于高温环境下的稳定导电",
            en: "Nickel based busbar for stable conductivity in high temperature environments"
        },
        type: "3d",
        model: "6.glb",
        image: "logo_wcon.webp", // 产品图片
        thumbnail: "6.glb",
        tags: ["popular"],
        cameraPosition: { alpha: Math.PI / 4, beta: Math.PI / 1.5, radius: 8 }
    },

    // 图片产品 - 刚性母线
    {
        id: 101,
        name: "刚性母线 - 1",
        description: "高性能刚性母线产品",
        type: "image",
        image: "images/刚性母线/1.webp",
        category: "刚性母线"
    },
    {
        id: 102,
        name: "刚性母线 - 2",
        description: "高性能刚性母线产品",
        type: "image",
        image: "images/刚性母线/2.webp",
        category: "刚性母线"
    },
    {
        id: 103,
        name: "刚性母线 - 3",
        description: "高性能刚性母线产品",
        type: "image",
        image: "images/刚性母线/3.webp",
        category: "刚性母线"
    },
    {
        id: 104,
        name: "刚性母线 - 4",
        description: "高性能刚性母线产品",
        type: "image",
        image: "images/刚性母线/4.webp",
        category: "刚性母线"
    },
    {
        id: 105,
        name: "刚性母线 - 5",
        description: "高性能刚性母线产品",
        type: "image",
        image: "images/刚性母线/5.webp",
        category: "刚性母线"
    },
    {
        id: 106,
        name: "刚性母线 - 6",
        description: "高性能刚性母线产品",
        type: "image",
        image: "images/刚性母线/6.webp",
        category: "刚性母线"
    },

    // 叠层母排
    {
        id: 201,
        name: "叠层母排 - 1",
        description: "高效叠层母排系统",
        type: "image",
        image: "images/叠层母排/1.webp",
        category: "叠层母排"
    },
    {
        id: 202,
        name: "叠层母排 - 2",
        description: "高效叠层母排系统",
        type: "image",
        image: "images/叠层母排/2.webp",
        category: "叠层母排"
    },
    {
        id: 203,
        name: "叠层母排 - 3",
        description: "高效叠层母排系统",
        type: "image",
        image: "images/叠层母排/3.webp",
        category: "叠层母排"
    },
    {
        id: 204,
        name: "叠层母排 - 4",
        description: "高效叠层母排系统",
        type: "image",
        image: "images/叠层母排/4.webp",
        category: "叠层母排"
    },

    // 导电排母线板
    {
        id: 301,
        name: "导电排母线板 - 1",
        description: "高性能导电排母线板",
        type: "image",
        image: "images/导电排母线板/1.webp",
        category: "导电排母线板"
    },
    {
        id: 302,
        name: "导电排母线板 - 2",
        description: "高性能导电排母线板",
        type: "image",
        image: "images/导电排母线板/2.webp",
        category: "导电排母线板"
    },
    {
        id: 303,
        name: "导电排母线板 - 3",
        description: "高性能导电排母线板",
        type: "image",
        image: "images/导电排母线板/3.webp",
        category: "导电排母线板"
    },
    {
        id: 304,
        name: "导电排母线板 - 4",
        description: "高性能导电排母线板",
        type: "image",
        image: "images/导电排母线板/4.webp",
        category: "导电排母线板"
    },
    {
        id: 305,
        name: "导电排母线板 - 5",
        description: "高性能导电排母线板",
        type: "image",
        image: "images/导电排母线板/5.webp",
        category: "导电排母线板"
    },
    {
        id: 306,
        name: "导电排母线板 - 6",
        description: "高性能导电排母线板",
        type: "image",
        image: "images/导电排母线板/6.webp",
        category: "导电排母线板"
    },
    {
        id: 307,
        name: "导电排母线板 - 7",
        description: "高性能导电排母线板",
        type: "image",
        image: "images/导电排母线板/7.webp",
        category: "导电排母线板"
    },
    {
        id: 308,
        name: "导电排母线板 - 8",
        description: "高性能导电排母线板",
        type: "image",
        image: "images/导电排母线板/8.webp",
        category: "导电排母线板"
    },

    // 柔性母线
    {
        id: 401,
        name: "柔性母线 - 1",
        description: "高柔韧性母线系统",
        type: "image",
        image: "images/柔性母线/1.webp",
        category: "柔性母线"
    },
    {
        id: 402,
        name: "柔性母线 - 2",
        description: "高柔韧性母线系统",
        type: "image",
        image: "images/柔性母线/2.webp",
        category: "柔性母线"
    },
    {
        id: 403,
        name: "柔性母线 - 3",
        description: "高柔韧性母线系统",
        type: "image",
        image: "images/柔性母线/3.webp",
        category: "柔性母线"
    },
    {
        id: 404,
        name: "柔性母线 - 4",
        description: "高柔韧性母线系统",
        type: "image",
        image: "images/柔性母线/4.webp",
        category: "柔性母线"
    },
    {
        id: 405,
        name: "柔性母线 - 5",
        description: "高柔韧性母线系统",
        type: "image",
        image: "images/柔性母线/5.webp",
        category: "柔性母线"
    },
    {
        id: 406,
        name: "柔性母线 - 6",
        description: "高柔韧性母线系统",
        type: "image",
        image: "images/柔性母线/6.webp",
        category: "柔性母线"
    },
    {
        id: 407,
        name: "柔性母线 - 7",
        description: "高柔韧性母线系统",
        type: "image",
        image: "images/柔性母线/7.webp",
        category: "柔性母线"
    },
    {
        id: 408,
        name: "柔性母线 - 8",
        description: "高柔韧性母线系统",
        type: "image",
        image: "images/柔性母线/8.webp",
        category: "柔性母线"
    },
    {
        id: 409,
        name: "柔性母线 - 9",
        description: "高柔韧性母线系统",
        type: "image",
        image: "images/柔性母线/9.webp",
        category: "柔性母线"
    },
    {
        id: 410,
        name: "柔性母线 - 10",
        description: "高柔韧性母线系统",
        type: "image",
        image: "images/柔性母线/10.webp",
        category: "柔性母线"
    },

    // 柔性电路刺破压接
    {
        id: 501,
        name: "柔性电路刺破压接 - 1",
        description: "创新柔性电路刺破压接技术",
        type: "image",
        image: "images/柔性电路刺破压接/1.webp",
        category: "柔性电路刺破压接"
    },
    {
        id: 502,
        name: "柔性电路刺破压接 - 2",
        description: "创新柔性电路刺破压接技术",
        type: "image",
        image: "images/柔性电路刺破压接/2.webp",
        category: "柔性电路刺破压接"
    },
    {
        id: 503,
        name: "柔性电路刺破压接 - 3",
        description: "创新柔性电路刺破压接技术",
        type: "image",
        image: "images/柔性电路刺破压接/3.webp",
        category: "柔性电路刺破压接"
    },
    {
        id: 504,
        name: "柔性电路刺破压接 - 4",
        description: "创新柔性电路刺破压接技术",
        type: "image",
        image: "images/柔性电路刺破压接/4.webp",
        category: "柔性电路刺破压接"
    },

    // 汇流排
    {
        id: 601,
        name: "汇流排 - 1",
        description: "高效能汇流排系统",
        type: "image",
        image: "images/汇流排/1.webp",
        category: "汇流排"
    },
    {
        id: 602,
        name: "汇流排 - 2",
        description: "高效能汇流排系统",
        type: "image",
        image: "images/汇流排/2.webp",
        category: "汇流排"
    },

    // 铝基导电排
    {
        id: 701,
        name: "铝基导电排 - 1",
        description: "轻量化铝基导电排",
        type: "image",
        image: "images/铝基导电排/1.webp",
        category: "铝基导电排"
    },
    {
        id: 702,
        name: "铝基导电排 - 2",
        description: "轻量化铝基导电排",
        type: "image",
        image: "images/铝基导电排/2.webp",
        category: "铝基导电排"
    },
    {
        id: 703,
        name: "铝基导电排 - 3",
        description: "轻量化铝基导电排",
        type: "image",
        image: "images/铝基导电排/3.webp",
        category: "铝基导电排"
    },
    {
        id: 704,
        name: "铝基导电排 - 4",
        description: "轻量化铝基导电排",
        type: "image",
        image: "images/铝基导电排/4.webp",
        category: "铝基导电排"
    },
    {
        id: 705,
        name: "铝基导电排 - 5",
        description: "轻量化铝基导电排",
        type: "image",
        image: "images/铝基导电排/5.webp",
        category: "铝基导电排"
    },
    {
        id: 706,
        name: "铝基导电排 - 6",
        description: "轻量化铝基导电排",
        type: "image",
        image: "images/铝基导电排/6.webp",
        category: "铝基导电排"
    },
    {
        id: 707,
        name: "铝基导电排 - 7",
        description: "轻量化铝基导电排",
        type: "image",
        image: "images/铝基导电排/7.webp",
        category: "铝基导电排"
    },
    {
        id: 708,
        name: "铝基导电排 - 8",
        description: "轻量化铝基导电排",
        type: "image",
        image: "images/铝基导电排/8.webp",
        category: "铝基导电排"
    },
    {
        id: 709,
        name: "铝基导电排 - 9",
        description: "轻量化铝基导电排",
        type: "image",
        image: "images/铝基导电排/9.webp",
        category: "铝基导电排"
    },
    {
        id: 710,
        name: "铝基导电排 - 10",
        description: "轻量化铝基导电排",
        type: "image",
        image: "images/铝基导电排/10.webp",
        category: "铝基导电排"
    },
    {
        id: 711,
        name: "铝基导电排 - 11",
        description: "轻量化铝基导电排",
        type: "image",
        image: "images/铝基导电排/11.webp",
        category: "铝基导电排"
    },

    // 镍基导电排
    {
        id: 801,
        name: "镍基导电排 - 1",
        description: "高耐腐蚀镍基导电排",
        type: "image",
        image: "images/镍基导电排/1.webp",
        category: "镍基导电排"
    },
    {
        id: 802,
        name: "镍基导电排 - 2",
        description: "高耐腐蚀镍基导电排",
        type: "image",
        image: "images/镍基导电排/2.webp",
        category: "镍基导电排"
    },
    {
        id: 803,
        name: "镍基导电排 - 3",
        description: "高耐腐蚀镍基导电排",
        type: "image",
        image: "images/镍基导电排/3.webp",
        category: "镍基导电排"
    },
    {
        id: 804,
        name: "镍基导电排 - 4",
        description: "高耐腐蚀镍基导电排",
        type: "image",
        image: "images/镍基导电排/4.webp",
        category: "镍基导电排"
    },
    {
        id: 805,
        name: "镍基导电排 - 5",
        description: "高耐腐蚀镍基导电排",
        type: "image",
        image: "images/镍基导电排/5.webp",
        category: "镍基导电排"
    },
    {
        id: 806,
        name: "镍基导电排 - 6",
        description: "高耐腐蚀镍基导电排",
        type: "image",
        image: "images/镍基导电排/6.webp",
        category: "镍基导电排"
    }
];

const IS_MOBILE = /Mobi|Android/i.test(navigator.userAgent) || window.innerWidth <= 768;

// 根据设备类型，使用不同的渲染器管理策略
let thumbnailRenderers = {}; // 移动端: 由IntersectionObserver管理
let pcRenderers = []; // PC端: 一次性全部加载并保持
let disposeTimers = {}; // 存储模型清除的定时器
let productIntersectionObserver;

// 缩略图渲染器类
class ModelThumbnailRenderer {
    constructor(canvas, modelPath, options = {}) {
        this.canvas = canvas;
        this.modelPath = modelPath;
        this.options = Object.assign({
            cameraPosition: { alpha: -Math.PI / 2, beta: Math.PI / 2.5, radius: 15 },
            autoRotate: false, // 默认关闭自动旋转
            clearColor: new BABYLON.Color4(1, 1, 1, 1) // 纯白色背景
        }, options);

        this.engine = null;
        this.scene = null;
        this.modelRoot = null;
        this.isDisposed = false;
        this.isRendering = false; // 新增：渲染状态标志

        this.init();
    }

    pause() {
        if (!this.isRendering) return;
        this.engine.stopRenderLoop();
        this.isRendering = false;
        console.log(`渲染已暂停: ${this.modelPath}`);
    }

    resume() {
        if (this.isRendering || this.isDisposed) return;
        this.engine.runRenderLoop(() => {
            if (!this.isDisposed) {
                this.scene.render();
            }
        });
        this.isRendering = true;
        console.log(`渲染已恢复: ${this.modelPath}`);
    }

    init() {
        // 创建引擎
        this.engine = new BABYLON.Engine(this.canvas, true, {
            preserveDrawingBuffer: true,
            stencil: true,
            adaptToDeviceRatio: true,
            powerPreference: "high-performance",
            antialias: false // 关闭抗锯齿以提高性能
        });

        // 创建场景
        this.scene = new BABYLON.Scene(this.engine);
        this.scene.clearColor = this.options.clearColor;

        // 创建相机
        const camera = new BABYLON.ArcRotateCamera(
            "camera",
            this.options.cameraPosition.alpha,
            this.options.cameraPosition.beta,
            this.options.cameraPosition.radius,
            new BABYLON.Vector3(0, 0, 0),
            this.scene
        );
        camera.attachControl(this.canvas, true);
        camera.wheelPrecision = 50;
        camera.lowerRadiusLimit = 5;
        camera.upperRadiusLimit = 30;

        // 创建光源
        const light1 = new BABYLON.HemisphericLight("light1", new BABYLON.Vector3(1, 1, 0), this.scene);
        light1.intensity = 0.7;

        const light2 = new BABYLON.DirectionalLight("light2", new BABYLON.Vector3(0, -1, 1), this.scene);
        light2.intensity = 0.5;

        // 添加环境贴图（共享资源）- 降低分辨率提高性能
        const hdrTexture = new BABYLON.HDRCubeTexture("./HDRguitar.hdr", this.scene, 192);
        this.scene.environmentTexture = hdrTexture;
        this.scene.environmentIntensity = 1;

        // 性能优化设置
        this.scene.blockMaterialDirtyMechanism = true;
        this.scene.skipPointerMovePicking = true;

        // 加载模型
        BABYLON.SceneLoader.ImportMeshAsync("", "./", this.modelPath, this.scene).then((result) => {
            // 加载完成后的处理
            this.meshes = result.meshes;
            this.modelRoot = this.meshes[0];

            // 应用PBR材质
            this.applyPBRMaterials();

            // 调整模型大小和位置
            this.fitModelToView();

            // 如果设置了自动旋转
            if (this.options.autoRotate) {
                /*
                this.scene.registerBeforeRender(() => {
                    if (this.modelRoot && !this.isDisposed) {
                        this.modelRoot.rotation.y += 0.005; // 降低旋转速度提高性能
                    }
                });
                */
            }

            // 添加触摸旋转控制
            let previousTouchX = 0;
            let rotationSpeed = 0;

            this.canvas.addEventListener("touchstart", (evt) => {
                if (!this.isDisposed) {
                    previousTouchX = evt.touches[0].clientX;
                    evt.preventDefault();
                }
            });

            this.canvas.addEventListener("touchmove", (evt) => {
                if (!this.isDisposed) {
                    const touch = evt.touches[0];
                    const deltaX = touch.clientX - previousTouchX;

                    if (this.modelRoot) {
                        this.modelRoot.rotation.y += deltaX * 0.005;
                    }

                    previousTouchX = touch.clientX;
                    rotationSpeed = deltaX * 0.001;

                    evt.preventDefault();
                }
            });

            this.canvas.addEventListener("touchend", (evt) => {
                if (!this.isDisposed) {
                    let inertia = rotationSpeed;
                    evt.preventDefault();

                    if (Math.abs(inertia) > 0.001) {
                        let inertiaDamping = () => {
                            if (!this.isDisposed && Math.abs(inertia) > 0.0001) {
                                if (this.modelRoot) {
                                    this.modelRoot.rotation.y += inertia;
                                }
                                inertia *= 0.95;
                                requestAnimationFrame(inertiaDamping);
                            }
                        };
                        inertiaDamping();
                    }
                }
            });

            // 隐藏对应的加载中指示器
            const loadingElement = this.canvas.parentElement.querySelector('.model-loading');
            if (loadingElement) {
                loadingElement.style.display = 'none';
            }
        }).catch(error => {
            console.error("加载模型时出错:", error);
            // 显示错误信息
            const loadingElement = this.canvas.parentElement.querySelector('.model-loading');
            if (loadingElement) {
                const errorDiv = loadingElement.querySelector('[data-lang-key="loading_text"]');
                if (errorDiv) {
                    const currentLang = getCurrentLanguage();
                    errorDiv.textContent = currentLang === 'en' ?
                        "Failed to load model. Please try again." :
                        "加载模型失败，请重试";
                }
            }
        });

        // 开始渲染循环
        this.resume(); // 使用resume方法启动

        // 处理窗口大小变化
        window.addEventListener("resize", () => {
            if (!this.isDisposed) {
                this.engine.resize();
            }
        });
    }

    applyPBRMaterials() {
        this.meshes.forEach(mesh => {
            if (mesh.material) {
                // 简化版本的PBR材质应用
                if (!(mesh.material instanceof BABYLON.PBRMaterial)) {
                    const pbr = new BABYLON.PBRMaterial("pbr-" + mesh.name, this.scene);

                    // 从原材质复制属性
                    if (mesh.material.albedoTexture) {
                        pbr.albedoTexture = mesh.material.albedoTexture;
                    } else if (mesh.material.diffuseTexture) {
                        pbr.albedoTexture = mesh.material.diffuseTexture;
                    }

                    if (mesh.material.bumpTexture) {
                        pbr.bumpTexture = mesh.material.bumpTexture;
                    }

                    // 设置金属和粗糙度属性
                    pbr.metallic = 0.5;
                    pbr.roughness = 0.4;

                    // 应用新材质
                    mesh.material = pbr;
                }
            }
        });
    }

    fitModelToView() {
        if (!this.meshes || this.meshes.length === 0) return;

        // 计算模型的边界信息
        let min = new BABYLON.Vector3(Number.MAX_VALUE, Number.MAX_VALUE, Number.MAX_VALUE);
        let max = new BABYLON.Vector3(Number.MIN_VALUE, Number.MIN_VALUE, Number.MIN_VALUE);

        for (let i = 0; i < this.meshes.length; i++) {
            if (this.meshes[i].getBoundingInfo) {
                const boundingInfo = this.meshes[i].getBoundingInfo();
                const meshMin = boundingInfo.boundingBox.minimumWorld;
                const meshMax = boundingInfo.boundingBox.maximumWorld;

                min = BABYLON.Vector3.Minimize(min, meshMin);
                max = BABYLON.Vector3.Maximize(max, meshMax);
            }
        }

        const size = max.subtract(min);
        const maxSize = Math.max(size.x, size.y, size.z);

        // 调整大小
        if (maxSize > 10 || maxSize < 1) {
            const scaleFactor = 5 / maxSize;
            this.modelRoot.scaling = new BABYLON.Vector3(scaleFactor, scaleFactor, scaleFactor);
        }

        // 将模型放置在场景中心
        const center = min.add(size.scale(0.5));
        this.modelRoot.position = BABYLON.Vector3.Zero().subtract(center);

        // ------------------- [可在此处设置缩略图模型的初始位置和旋转] -------------------
        // 如果需要自定义模型的初始位置，请取消以下代码的注释并修改值
        // this.modelRoot.position = new BABYLON.Vector3(0, -1, 0); // 示例：将模型沿Y轴下移1个单位

        // 如果需要自定义模型的初始旋转（弧度制），请取消以下代码的注释并修改值
        // this.modelRoot.rotation = new BABYLON.Vector3(0, Math.PI / 18, 0); // 示例：将模型沿Y轴旋转45度
        // -----------------------------------------------------------------------------
    }

    dispose() {
        if (this.isDisposed) return;
        this.isDisposed = true;

        if (IS_MOBILE) {
            // 移动端：完全清理资源以释放内存
            if (this.scene) {
                this.scene.dispose();
                this.scene = null;
            }
            if (this.engine) {
                this.engine.dispose();
                this.engine = null;
            }
            this.modelRoot = null;
            this.meshes = null;
            this.canvas = null;
        } else {
            // PC端：只暂停渲染循环，保留资源以便快速恢复
            if (this.scene) {
                this.scene.detachControl();
                // 停止渲染循环但不销毁资源
                if (this.engine) {
                    this.engine.stopRenderLoop();
                }
            }
        }
    }
}

// 详情视图类（模型查看器）
class ModelViewer {
    constructor(canvas, loadingElement) {
        this.canvas = canvas;
        this.loadingElement = loadingElement;

        // 获取控制元素
        this.autoRotateBtn = document.getElementById("autoRotateBtn");
        this.scaleSlider = document.getElementById("scaleSlider");
        this.envIntensitySlider = document.getElementById("envIntensitySlider");
        this.metallicSlider = document.getElementById("metallicSlider");
        this.roughnessSlider = document.getElementById("roughnessSlider");
        this.resetBtn = document.getElementById("resetBtn");

        // 控制变量
        this.autoRotate = false; // 默认关闭自动旋转
        this.modelRoot = null;
        this.originalPosition = null;
        this.originalRotation = null;
        this.originalScale = null;
        this.scene = null;
        this.meshes = [];
        this.engine = null;
        this.originalMaterialProperties = new Map();

        // 初始化引擎和场景
        this.engine = new BABYLON.Engine(this.canvas, true, {
            adaptToDeviceRatio: true,
            powerPreference: "high-performance",
            antialias: false // 关闭抗锯齿以提高性能
        });
        this.scene = new BABYLON.Scene(this.engine);
        this.scene.clearColor = new BABYLON.Color4(1, 1, 1, 1); // 纯白色背景

        // 启动渲染循环
        this.engine.runRenderLoop(() => {
            this.scene.render();
        });

        // 处理窗口大小变化
        window.addEventListener("resize", () => {
            this.engine.resize();
        });

        // 设置事件监听器
        this.setupEventListeners();

        // 添加触摸旋转控制
        let previousTouchX = 0;
        let rotationSpeed = 0;

        this.canvas.addEventListener("touchstart", (evt) => {
            previousTouchX = evt.touches[0].clientX;
            evt.preventDefault();
        });

        this.canvas.addEventListener("touchmove", (evt) => {
            const touch = evt.touches[0];
            const deltaX = touch.clientX - previousTouchX;

            if (this.modelRoot) {
                this.modelRoot.rotation.y += deltaX * 0.005;
            }

            previousTouchX = touch.clientX;
            rotationSpeed = deltaX * 0.001;

            evt.preventDefault();
        });

        this.canvas.addEventListener("touchend", (evt) => {
            let inertia = rotationSpeed;
            evt.preventDefault();

            if (Math.abs(inertia) > 0.001) {
                let inertiaDamping = () => {
                    if (Math.abs(inertia) > 0.0001) {
                        if (this.modelRoot) {
                            this.modelRoot.rotation.y += inertia;
                        }
                        inertia *= 0.95;
                        requestAnimationFrame(inertiaDamping);
                    }
                };
                inertiaDamping();
            }
        });
    }

    loadModel(modelPath, options = {}) {
        // 显示加载屏幕
        this.loadingElement.style.display = "flex";

        // 重置场景（如果已有模型）
        if (this.modelRoot) {
            this.scene.dispose();
            this.scene = new BABYLON.Scene(this.engine);
            this.scene.clearColor = new BABYLON.Color4(1, 1, 1, 1); // 纯白色背景
            this.modelRoot = null;
            this.meshes = [];
            this.originalMaterialProperties.clear();
        }

        // 创建相机
        const cameraPosition = options.cameraPosition || {
            alpha: -Math.PI / 2,
            beta: Math.PI / 2.5,
            radius: 15
        };

        const camera = new BABYLON.ArcRotateCamera(
            "camera",
            cameraPosition.alpha,
            cameraPosition.beta,
            cameraPosition.radius,
            new BABYLON.Vector3(0, 0, 0),
            this.scene
        );
        camera.attachControl(this.canvas, true);
        camera.wheelPrecision = 50;
        camera.lowerRadiusLimit = 5;
        camera.upperRadiusLimit = 30;

        // 创建光源
        const light1 = new BABYLON.HemisphericLight("light1", new BABYLON.Vector3(1, 1, 0), this.scene);
        light1.intensity = 0.3;

        const light2 = new BABYLON.DirectionalLight("light2", new BABYLON.Vector3(0, -1, 1), this.scene);
        light2.intensity = 0.3;

        // 加载HDR环境贴图 - 降低分辨率提高性能
        const hdrTexture = new BABYLON.HDRCubeTexture("./HDRguitar.hdr", this.scene, 192);
        this.scene.environmentTexture = hdrTexture;
        this.scene.environmentIntensity = parseFloat(this.envIntensitySlider.value);

        // 性能优化设置
        this.scene.blockMaterialDirtyMechanism = true;
        this.scene.skipPointerMovePicking = true;

        // 加载模型
        BABYLON.SceneLoader.ImportMeshAsync("", "./", modelPath, this.scene).then((result) => {
            // 加载完成后处理
            this.meshes = result.meshes;
            this.modelRoot = this.meshes[0];

            // 应用PBR材质
            this.convertToPBRMaterials(this.meshes);

            // 调整模型大小和位置
            const boundingInfo = this.calculateBoundingInfo(this.meshes);
            const size = boundingInfo.max.subtract(boundingInfo.min);
            const maxSize = Math.max(size.x, size.y, size.z);

            if (maxSize > 10 || maxSize < 1) {
                const scaleFactor = 5 / maxSize;
                this.modelRoot.scaling = new BABYLON.Vector3(scaleFactor, scaleFactor, scaleFactor);
            }

            // 将模型放置在场景中心
            const center = boundingInfo.min.add(size.scale(0.5));
            this.modelRoot.position = BABYLON.Vector3.Zero().subtract(center);

            // ------------------- [可在此处设置模型的初始位置和旋转] -------------------
            // 如果需要自定义模型的初始位置，请取消以下代码的注释并修改值
            // this.modelRoot.position = new BABYLON.Vector3(0, -1, 0); // 示例：将模型沿Y轴下移1个单位

            // 如果需要自定义模型的初始旋转（弧度制），请取消以下代码的注释并修改值
            this.modelRoot.rotation = new BABYLON.Vector3(0, Math.PI / 4, 0); // 示例：将模型沿Y轴旋转45度
            // --------------------------------------------------------------------------

            // 保存原始状态，用于重置
            this.originalPosition = this.modelRoot.position.clone();
            this.originalRotation = this.modelRoot.rotation.clone();
            this.originalScale = this.modelRoot.scaling.clone();

            // 填充部位列表
            this.populatePartsList(this.meshes);

            // 设置点击事件以高亮选中的部位
            this.setupMeshSelection(this.meshes);

            // 隐藏加载屏幕
            this.loadingElement.style.display = "none";
        }).catch((error) => {
            console.error("加载模型时出错:", error);
            // 使用当前语言显示错误信息
            const currentLang = getCurrentLanguage();
            if (currentLang === 'en') {
                this.loadingElement.textContent = "Failed to load model. Please try again.";
            } else {
                this.loadingElement.textContent = "加载模型失败，请重试";
            }
        });
    }

    convertToPBRMaterials(meshes) {
        meshes.forEach(mesh => {
            if (mesh.material) {
                if (!(mesh.material instanceof BABYLON.PBRMaterial)) {
                    const pbr = new BABYLON.PBRMaterial("pbr-" + mesh.name, this.scene);

                    if (mesh.material.albedoTexture) {
                        pbr.albedoTexture = mesh.material.albedoTexture;
                    } else if (mesh.material.diffuseTexture) {
                        pbr.albedoTexture = mesh.material.diffuseTexture;
                    }

                    if (mesh.material.bumpTexture) {
                        pbr.bumpTexture = mesh.material.bumpTexture;
                    }

                    // 检测是否为金属部分
                    let isMetallic = this.detectMetallicPart(mesh);

                    if (isMetallic) {
                        pbr.metallic = 0.9;
                        pbr.roughness = 0.1;
                        pbr.environmentIntensity = 1.2;
                    } else {
                        pbr.metallic = parseFloat(this.metallicSlider.value);
                        pbr.roughness = parseFloat(this.roughnessSlider.value);
                        pbr.environmentIntensity = parseFloat(this.envIntensitySlider.value);
                    }

                    // 保存原始材质属性
                    this.originalMaterialProperties.set(mesh.id, {
                        metallic: pbr.metallic,
                        roughness: pbr.roughness,
                        environmentIntensity: pbr.environmentIntensity
                    });

                    mesh.material = pbr;
                } else {
                    // 已经是PBR材质
                    let isMetallic = this.detectMetallicPart(mesh);

                    if (isMetallic) {
                        mesh.material.metallic = 0.9;
                        mesh.material.roughness = 0.1;
                        mesh.material.environmentIntensity = 1.2;
                    } else {
                        mesh.material.metallic = parseFloat(this.metallicSlider.value);
                        mesh.material.roughness = parseFloat(this.roughnessSlider.value);
                        mesh.material.environmentIntensity = parseFloat(this.envIntensitySlider.value);
                    }

                    this.originalMaterialProperties.set(mesh.id, {
                        metallic: mesh.material.metallic,
                        roughness: mesh.material.roughness,
                        environmentIntensity: mesh.material.environmentIntensity
                    });
                }
            }
        });
    }

    detectMetallicPart(mesh) {
        let isMetallic = false;

        // 通过材质名称判断
        if (mesh.material && mesh.material.name && (
            mesh.material.name.toLowerCase().includes("metal") ||
            mesh.material.name.toLowerCase().includes("chrome") ||
            mesh.material.name.toLowerCase().includes("steel") ||
            mesh.material.name.toLowerCase().includes("iron") ||
            mesh.material.name.toLowerCase().includes("gold") ||
            mesh.material.name.toLowerCase().includes("silver")
        )) {
            isMetallic = true;
        }

        // 通过网格名称判断
        if (mesh.name && (
            mesh.name.toLowerCase().includes("metal") ||
            mesh.name.toLowerCase().includes("chrome") ||
            mesh.name.toLowerCase().includes("steel") ||
            mesh.name.toLowerCase().includes("iron") ||
            mesh.name.toLowerCase().includes("gold") ||
            mesh.name.toLowerCase().includes("silver")
        )) {
            isMetallic = true;
        }

        return isMetallic;
    }

    calculateBoundingInfo(meshes) {
        let min = new BABYLON.Vector3(Number.MAX_VALUE, Number.MAX_VALUE, Number.MAX_VALUE);
        let max = new BABYLON.Vector3(Number.MIN_VALUE, Number.MIN_VALUE, Number.MIN_VALUE);

        for (let i = 0; i < meshes.length; i++) {
            if (meshes[i].getBoundingInfo) {
                const boundingInfo = meshes[i].getBoundingInfo();
                const meshMin = boundingInfo.boundingBox.minimumWorld;
                const meshMax = boundingInfo.boundingBox.maximumWorld;

                min = BABYLON.Vector3.Minimize(min, meshMin);
                max = BABYLON.Vector3.Maximize(max, meshMax);
            }
        }

        const size = max.subtract(min);
        const maxSize = Math.max(size.x, size.y, size.z);

        // 调整大小
        if (maxSize > 10 || maxSize < 1) {
            const scaleFactor = 5 / maxSize;
            this.modelRoot.scaling = new BABYLON.Vector3(scaleFactor, scaleFactor, scaleFactor);
        }

        // 将模型放置在场景中心
        const center = min.add(size.scale(0.5));
        this.modelRoot.position = BABYLON.Vector3.Zero().subtract(center);

        // ------------------- [可在此处设置模型的初始位置和旋转] -------------------
        // 如果需要自定义模型的初始位置，请取消以下代码的注释并修改值
        // this.modelRoot.position = new BABYLON.Vector3(0, -1, 0); // 示例：将模型沿Y轴下移1个单位

        // 如果需要自定义模型的初始旋转（弧度制），请取消以下代码的注释并修改值
        this.modelRoot.rotation = new BABYLON.Vector3(0, Math.PI / 4, 0); // 示例：将模型沿Y轴旋转45度
        // --------------------------------------------------------------------------

        // 保存原始状态，用于重置
        this.originalPosition = this.modelRoot.position.clone();
        this.originalRotation = this.modelRoot.rotation.clone();
        this.originalScale = this.modelRoot.scaling.clone();

        return {
            min: min,
            max: max
        };
    }

    updateEnvironmentIntensity(value) {
        if (!this.scene) return;

        this.scene.environmentIntensity = value;

        this.scene.materials.forEach(material => {
            if (material instanceof BABYLON.PBRMaterial) {
                let isMetallic = material.metallic > 0.7;

                if (isMetallic) {
                    material.environmentIntensity = Math.max(value, 1.0);
                } else {
                    material.environmentIntensity = value;
                }
            }
        });
    }

    updateMetallic(value) {
        if (!this.scene) return;

        this.scene.materials.forEach(material => {
            if (material instanceof BABYLON.PBRMaterial) {
                if (material.metallic <= 0.7) {
                    material.metallic = value;
                }
            }
        });
    }

    updateRoughness(value) {
        if (!this.scene) return;

        this.scene.materials.forEach(material => {
            if (material instanceof BABYLON.PBRMaterial) {
                if (material.metallic <= 0.7) {
                    material.roughness = value;
                }
            }
        });
    }

    setupEventListeners() {
        this.autoRotateBtn.addEventListener("click", () => {
            this.autoRotate = !this.autoRotate;
            this.autoRotateBtn.textContent = this.autoRotate ? "关闭" : "开启";
        });

        this.scaleSlider.addEventListener("input", () => {
            if (this.modelRoot && this.originalScale) {
                const scaleFactor = parseFloat(this.scaleSlider.value);
                this.modelRoot.scaling = new BABYLON.Vector3(
                    this.originalScale.x * scaleFactor,
                    this.originalScale.y * scaleFactor,
                    this.originalScale.z * scaleFactor
                );
            }
        });

        this.envIntensitySlider.addEventListener("input", () => {
            const value = parseFloat(this.envIntensitySlider.value);
            this.updateEnvironmentIntensity(value);
        });

        this.metallicSlider.addEventListener("input", () => {
            const value = parseFloat(this.metallicSlider.value);
            this.updateMetallic(value);
        });

        this.roughnessSlider.addEventListener("input", () => {
            const value = parseFloat(this.roughnessSlider.value);
            this.updateRoughness(value);
        });

        this.resetBtn.addEventListener("click", () => {
            if (this.modelRoot && this.originalPosition && this.originalRotation && this.originalScale) {
                this.modelRoot.position = this.originalPosition.clone();
                this.modelRoot.rotation = this.originalRotation.clone();
                this.modelRoot.scaling = this.originalScale.clone();
                this.scaleSlider.value = "1";
                this.envIntensitySlider.value = "0.8";
                this.metallicSlider.value = "0.5";
                this.roughnessSlider.value = "0.4";
                this.updateEnvironmentIntensity(0.8);
                this.updateMetallic(0.5);
                this.updateRoughness(0.4);

                // 重置材质
                this.meshes.forEach(mesh => {
                    if (mesh.material instanceof BABYLON.PBRMaterial) {
                        const originalProps = this.originalMaterialProperties.get(mesh.id);
                        if (originalProps) {
                            mesh.material.metallic = originalProps.metallic;
                            mesh.material.roughness = originalProps.roughness;
                            mesh.material.environmentIntensity = originalProps.environmentIntensity;
                        }
                    }
                });

                // 重置相机
                const camera = this.scene.activeCamera;
                camera.alpha = -Math.PI / 2;
                camera.beta = Math.PI / 2.5;
                camera.radius = 15;
            }
        });
    }
}

function setupProductObserver() {
    const options = {
        root: null,
        rootMargin: '300px 0px', // 增大预加载范围，提前加载用户即将看到的内容
        threshold: 0.01
    };

    const DISPOSE_DELAY = 20000; // 20秒延迟清除

    productIntersectionObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            const canvas = entry.target.querySelector('.model-canvas');
            if (!canvas) return;

            const canvasId = canvas.id;

            if (entry.isIntersecting) {
                // 如果模型重新进入视图，清除之前的清理定时器
                if (disposeTimers[canvasId]) {
                    clearTimeout(disposeTimers[canvasId]);
                    delete disposeTimers[canvasId];
                }

                // 如果模型不存在，创建它
                if (!thumbnailRenderers[canvasId] && canvas.dataset.model) {
                    const modelPath = canvas.dataset.model;
                    let cameraPosition;
                    try {
                        cameraPosition = JSON.parse(canvas.dataset.cameraPosition);
                    } catch (e) {
                        cameraPosition = undefined;
                    }

                    console.log(`加载3D模型: ${canvasId}, 路径: ${modelPath}`);
                    thumbnailRenderers[canvasId] = new ModelThumbnailRenderer(canvas, modelPath, {
                        cameraPosition: cameraPosition,
                        autoRotate: true
                    });
                }
            } else {
                // 模型离开视图，设置20秒后清理的定时器
                if (thumbnailRenderers[canvasId] && IS_MOBILE) {
                    // 确保不重复设置定时器
                    if (!disposeTimers[canvasId]) {
                        disposeTimers[canvasId] = setTimeout(() => {
                            if (thumbnailRenderers[canvasId]) {
                                thumbnailRenderers[canvasId].dispose();
                                delete thumbnailRenderers[canvasId];
                                console.log(`模型 ${canvasId} 已在离开视图20秒后被清理`);
                            }
                            delete disposeTimers[canvasId];
                        }, DISPOSE_DELAY);
                    }
                }
            }
        });
    }, options);

    // 立即加载可见的3D模型，不等待IntersectionObserver触发
    function loadInitialVisibleModels() {
        const productsGrid = document.getElementById('products3dGrid');
        if (!productsGrid) return;

        const productCards = productsGrid.querySelectorAll('.product-card');
        productCards.forEach(card => {
            const canvas = card.querySelector('.model-canvas');
            if (!canvas || !canvas.dataset.model) return;

            const canvasId = canvas.id;
            const rect = card.getBoundingClientRect();
            const isVisible = (
                rect.top < window.innerHeight &&
                rect.bottom > 0 &&
                rect.left < window.innerWidth &&
                rect.right > 0
            );

            if (isVisible && !thumbnailRenderers[canvasId]) {
                const modelPath = canvas.dataset.model;
                let cameraPosition;
                try {
                    cameraPosition = JSON.parse(canvas.dataset.cameraPosition);
                } catch (e) {
                    cameraPosition = undefined;
                }

                console.log(`初始加载可见的3D模型: ${canvasId}, 路径: ${modelPath}`);
                thumbnailRenderers[canvasId] = new ModelThumbnailRenderer(canvas, modelPath, {
                    cameraPosition: cameraPosition,
                    autoRotate: true
                });
            }

            // 同时添加观察器来管理其生命周期
            productIntersectionObserver.observe(card);
        });
    }

    // 在设置完观察器后，立即加载当前可见的模型
    setTimeout(loadInitialVisibleModels, 100);
}

// 获取当前语言
function getCurrentLanguage() {
    return localStorage.getItem('language') || 'zh';
}

// 根据当前语言获取产品名称
function getProductName(product) {
    const lang = getCurrentLanguage();
    if (product.title && product.title[lang]) {
        return product.title[lang];
    } else if (product.title && product.title.zh) {
        return product.title.zh; // 回退到中文
    } else if (lang === 'en' && product.nameEn) {
        return product.nameEn; // 兼容旧数据
    } else {
        return product.name || "产品名称";
    }
}

// 根据当前语言获取产品描述
function getProductDescription(product) {
    const lang = getCurrentLanguage();
    if (product.description && typeof product.description === 'object' && product.description[lang]) {
        return product.description[lang];
    } else if (product.description && typeof product.description === 'object' && product.description.zh) {
        return product.description.zh; // 回退到中文
    } else if (lang === 'en' && product.descriptionEn) {
        return product.descriptionEn; // 兼容旧数据
    } else {
        return product.description || "产品描述";
    }
}

// 3D产品展示区域
function display3DProducts(productsToDisplay) {
    const productsGrid = document.getElementById('products3dGrid');
    if (!productsGrid) return;
    productsGrid.innerHTML = '';

    // 创建一个IntersectionObserver来处理懒加载和渲染控制
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            const canvas = entry.target;
            if (entry.isIntersecting) {
                // 当元素进入视区
                if (!canvas.renderer) {
                    // 如果渲染器未初始化，则创建它（懒加载）
                    const modelPath = canvas.dataset.model;
                    const cameraPosition = JSON.parse(canvas.dataset.cameraPosition || '{}');
                    canvas.renderer = new ModelThumbnailRenderer(canvas, modelPath, {
                        cameraPosition: cameraPosition,
                        autoRotate: true
                    });
                } else {
                    // 如果渲染器已存在，则恢复渲染
                    canvas.renderer.resume();
                }
            } else {
                // 当元素离开视区
                if (canvas.renderer) {
                    // 如果渲染器存在，则暂停渲染
                    canvas.renderer.pause();
                }
            }
        });
    }, {
        threshold: 0.01, // 元素可见1%时触发
        rootMargin: "0px 0px 100px 0px" // 提前100px加载
    });

    // 为每个3D产品创建卡片，并设置观察器
    productsToDisplay.forEach((product, index) => {
        const card = document.createElement('div');
        card.className = 'product-card';
        card.dataset.productId = product.id;
        const canvasId = `product-canvas-${product.id}-${index}`;
        const currentLang = getCurrentLanguage();
        const loadingText = currentLang === 'en' ? 'Loading Model...' : '3D模型加载中...';

        card.innerHTML = `
            <div class="model-container">
                <img src="icon_3d.svg" alt="3D" class="model-3d-icon">
                <canvas class="model-canvas" id="${canvasId}"></canvas>
                <div class="model-loading">
                    <div class="spinner"></div>
                    <div data-lang-key="loading_text">${loadingText}</div>
                </div>
            </div>
            <div class="product-info">
                <h3 class="product-title">${getProductName(product)}</h3>
            </div>
        `;

        productsGrid.appendChild(card);

        const canvas = document.getElementById(canvasId);
        if (canvas) {
            // 将模型信息存储在canvas上，以便观察器使用
            canvas.dataset.model = product.model;
            if (product.cameraPosition) {
                canvas.dataset.cameraPosition = JSON.stringify(product.cameraPosition);
            }

            // 开始观察canvas
            observer.observe(canvas);
        }
    });
}

// 全部产品展示区域（只用图片）- 使用异步懒加载
function displayAllProducts(productsToDisplay) {
    const grid = document.getElementById('allProductsGrid');
    const lang = getCurrentLanguage();
    if (!grid) return;

    grid.innerHTML = ''; // Clear previous items

    if (productsToDisplay.length === 0) {
        grid.innerHTML = `<p data-lang-key="no_products_found">No products found.</p>`;
        applyLanguage(lang);
        return;
    }

    const fragment = document.createDocumentFragment();
    const isMobile = window.innerWidth < 768; // 检测是否为移动端

    productsToDisplay.forEach(product => {
        const card = document.createElement('div');
        card.className = 'product-card';
        card.setAttribute('data-category', product.category);

        // 在移动端显示产品名称，PC端不显示
        if (isMobile) {
            card.innerHTML = `
                <div class="model-container">
                    <img class="model-canvas" src="${product.image}" alt="${getProductName(product)}" style="object-fit:contain">
                </div>
                <div class="product-info">
                    <h4 class="product-title">${getProductName(product)}</h4>
                </div>
            `;
        } else {
            card.innerHTML = `
                <div class="model-container">
                    <img class="model-canvas" src="${product.image}" alt="${getProductName(product)}" style="object-fit:contain">
                </div>
            `;
        }

        // 添加点击事件处理
        card.addEventListener('click', () => {
            showProductDetail(product);
        });

        fragment.appendChild(card);
    });

    grid.appendChild(fragment);
}

function showProductDetail(product) {
    const lang = getCurrentLanguage();

    if (product.type === "3d") {
        const detailView = document.getElementById('detailView');
        detailView.style.display = 'block';
        detailView.setAttribute('data-current-product', product.id);

        document.getElementById('detailTitle').textContent = getProductName(product);
        document.getElementById('detailDescription').textContent = getProductDescription(product);

        if (detailViewer && detailViewer.loadModel) {
            detailViewer.loadModel(product.model, { cameraPosition: product.cameraPosition });
        }
    } else if (product.type === "image") {
        // 创建并显示图片灯箱
        const lightbox = document.createElement('div');
        lightbox.id = 'imageLightbox';
        lightbox.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.85);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 3000;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;

        const lightboxImage = document.createElement('img');
        lightboxImage.src = product.image;
        lightboxImage.style.cssText = `
            max-width: 90%;
            max-height: 90%;
            object-fit: contain;
            border-radius: 4px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.4);
            transform: scale(0.95);
            transition: transform 0.3s ease;
        `;

        const productName = document.createElement('div');
        productName.textContent = getProductName(product);
        productName.style.cssText = `
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            background-color: rgba(0,0,0,0.5);
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 16px;
        `;

        lightbox.appendChild(lightboxImage);
        lightbox.appendChild(productName);
        document.body.appendChild(lightbox);

        // 平滑显示
        requestAnimationFrame(() => {
            lightbox.style.opacity = '1';
            lightboxImage.style.transform = 'scale(1)';
        });

        // 点击关闭
        lightbox.addEventListener('click', () => {
            lightbox.style.opacity = '0';
            lightbox.addEventListener('transitionend', () => {
                document.body.removeChild(lightbox);
            }, { once: true });
        });
    }
}

function closeProductDetail() {
    document.getElementById('detailView').style.display = 'none';

    // 清理资源
    if (detailViewer.clearScene) {
        detailViewer.clearScene();
    }

    // 移除可能存在的产品图片
    const detailInfo = document.getElementById('detailInfo');
    const img = detailInfo.querySelector('img.detail-image');
    if (img) img.remove();
}

document.addEventListener('DOMContentLoaded', () => {
    // 立即初始化详情查看器
    const detailViewer = new ModelViewer(
        document.getElementById('detailCanvas'),
        document.getElementById('detailLoading')
    );

    // 添加页面卸载事件，清理所有资源
    window.addEventListener('beforeunload', () => {
        // 清除所有定时器
        Object.keys(disposeTimers).forEach(key => {
            clearTimeout(disposeTimers[key]);
        });

        // 清除所有渲染器
        Object.keys(thumbnailRenderers).forEach(key => {
            if (thumbnailRenderers[key]) thumbnailRenderers[key].dispose();
        });

        // 清除PC端渲染器
        pcRenderers.forEach(r => {
            if (r) r.dispose();
        });
    });

    // 监听语言变化事件
    window.addEventListener('languageChanged', function (e) {
        const newLang = e.detail.language;
        // 刷新3D产品区域
        const products3d = window.productData.filter(p => p.type === "3d");
        display3DProducts(products3d);

        // 如果详情弹窗正在显示，也更新其内容
        const detailView = document.getElementById('detailView');
        if (detailView && detailView.style.display === 'block') {
            const currentProductId = detailView.getAttribute('data-current-product');
            if (currentProductId) {
                const currentProduct = window.productData.find(p => p.id.toString() === currentProductId);
                if (currentProduct) {
                    document.getElementById('detailTitle').textContent = getProductName(currentProduct);
                    document.getElementById('detailDescription').textContent = getProductDescription(currentProduct);
                }
            }
        }
    });

    // 异步初始化所有产品展示
    function initProductsList() {
        // 检查DOM元素准备情况
        console.log('开始初始化产品列表...');
        const allProductsGrid = document.getElementById('allProductsGrid');
        if (!allProductsGrid) {
            console.error('allProductsGrid元素不存在，无法显示全部产品');
            return;
        }

        // 先初始化所有产品展示（图片）- 优先级更高
        console.log('开始加载所有产品图片...');
        const allProducts = window.productData.filter(p => p.type === "image");
        console.log(`过滤后图片产品数量: ${allProducts.length}`);

        // 移动端特殊处理 - 直接一次性加载所有产品以避免复杂的批处理逻辑
        if (IS_MOBILE) {
            try {
                console.log('移动端使用简化加载方式');
                allProductsGrid.innerHTML = '';

                // 直接生成所有产品HTML
                allProducts.forEach(product => {
                    const productElement = document.createElement('div');
                    productElement.className = 'product-card';
                    productElement.setAttribute('data-category', product.category || '');

                    productElement.innerHTML = `
                        <div class="model-container">
                            <img class="model-canvas" src="${product.image}" alt="${getProductName(product)}" style="object-fit:contain">
                        </div>
                        <div class="product-info">
                            <h4 class="product-title">${getProductName(product)}</h4>
                        </div>
                    `;

                    // 添加点击事件
                    productElement.addEventListener('click', () => {
                        showProductDetail(product);
                    });

                    allProductsGrid.appendChild(productElement);
                });

                console.log(`已加载 ${allProducts.length} 个产品到全部产品区域`);
            } catch (err) {
                console.error('移动端加载产品时出错:', err);
            }
        } else {
            // PC端使用常规处理
            displayAllProducts(allProducts);
        }

        // 先初始化产品观察器，然后延迟加载3D产品
        setupProductObserver();

        // 使用requestIdleCallback或setTimeout延迟加载3D产品（优先级较低）
        const loadModels = () => {
            console.log('开始加载3D产品模型...');
            const products3d = window.productData.filter(p => p.type === "3d");
            display3DProducts(products3d);
        };

        // 延迟加载3D模型
        if (window.requestIdleCallback) {
            requestIdleCallback(loadModels, { timeout: 2000 });
        } else {
            setTimeout(loadModels, 1000);
        }

        // 过滤器事件监听
        document.querySelectorAll('.filter-button').forEach(button => {
            button.addEventListener('click', function () {
                const filter = this.getAttribute('data-filter');

                // 设置激活状态
                document.querySelectorAll('.filter-button').forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');

                // 执行过滤
                filterAllProducts(filter);
            });
        });

        // 搜索框事件监听
        const searchBox = document.getElementById('searchAllBox');
        if (searchBox) {
            let debounceTimeout;
            searchBox.addEventListener('input', function () {
                clearTimeout(debounceTimeout);
                debounceTimeout = setTimeout(() => {
                    searchAllProducts(this.value);
                }, 300);
            });
        }
    }

    // 使用requestAnimationFrame确保在下一帧开始初始化
    requestAnimationFrame(() => {
        initProductsList();
    });

    function filter3DProducts(filter) {
        if (filter === 'all') {
            display3DProducts(window.productData.filter(p => p.type === "3d"));
        } else {
            const filteredProducts = window.productData.filter(p => p.type === "3d" && p.tags.includes(filter));
            display3DProducts(filteredProducts);
        }
    }

    function filterAllProducts(filter) {
        const allProducts = window.productData.filter(p => p.type === "image");
        const filtered = filter === 'all' ?
            allProducts :
            allProducts.filter(p => p.category === filter);

        // 使用移动端特殊处理或PC端常规处理
        if (IS_MOBILE) {
            try {
                console.log(`过滤产品 - 类别: ${filter}, 数量: ${filtered.length}`);
                const allProductsGrid = document.getElementById('allProductsGrid');
                if (!allProductsGrid) return;

                allProductsGrid.innerHTML = '';

                if (filtered.length === 0) {
                    allProductsGrid.innerHTML = '<div class="no-results">没有找到匹配的产品</div>';
                    return;
                }

                // 直接生成所有过滤后的产品HTML
                filtered.forEach(product => {
                    const productElement = document.createElement('div');
                    productElement.className = 'product-card';
                    productElement.setAttribute('data-category', product.category || '');

                    productElement.innerHTML = `
                        <div class="model-container">
                            <img class="model-canvas" src="${product.image}" alt="${getProductName(product)}" style="object-fit:contain">
                        </div>
                        <div class="product-info">
                            <h4 class="product-title">${getProductName(product)}</h4>
                        </div>
                    `;

                    // 添加点击事件
                    productElement.addEventListener('click', () => {
                        showProductDetail(product);
                    });

                    allProductsGrid.appendChild(productElement);
                });
            } catch (err) {
                console.error('移动端过滤产品时出错:', err);
            }
        } else {
            // PC端使用常规处理
            displayAllProducts(filtered);
        }
    }

    function search3DProducts(query) {
        const lowerCaseQuery = query.toLowerCase();
        const filteredProducts = window.productData.filter(p => {
            const name = getProductName(p);
            const description = getProductDescription(p);
            return name.toLowerCase().includes(lowerCaseQuery) ||
                description.toLowerCase().includes(lowerCaseQuery);
        });
        display3DProducts(filteredProducts);
    }

    function searchAllProducts(query) {
        const allProducts = window.productData.filter(p => p.type === "image");

        // 如果搜索查询为空，显示所有产品
        if (!query.trim()) {
            if (IS_MOBILE) {
                filterAllProducts('all'); // 使用过滤函数显示所有产品
            } else {
                displayAllProducts(allProducts);
            }
            return;
        }

        // 搜索匹配产品
        const queryLower = query.toLowerCase();
        const filteredProducts = allProducts.filter(product => {
            return product.name.toLowerCase().includes(queryLower) ||
                (product.category && product.category.toLowerCase().includes(queryLower));
        });

        // 使用移动端或PC端逻辑
        if (IS_MOBILE) {
            try {
                console.log(`搜索产品 - 查询: "${query}", 结果数量: ${filteredProducts.length}`);
                const allProductsGrid = document.getElementById('allProductsGrid');
                if (!allProductsGrid) return;

                allProductsGrid.innerHTML = '';

                if (filteredProducts.length === 0) {
                    allProductsGrid.innerHTML = '<div class="no-results">没有找到匹配的产品</div>';
                    return;
                }

                // 直接生成所有搜索结果的HTML
                filteredProducts.forEach(product => {
                    const productElement = document.createElement('div');
                    productElement.className = 'product-card';
                    productElement.setAttribute('data-category', product.category || '');

                    productElement.innerHTML = `
                        <div class="model-container">
                            <img class="model-canvas" src="${product.image}" alt="${product.name}" style="object-fit:contain">
                        </div>
                    `;

                    // 添加点击事件
                    productElement.addEventListener('click', () => {
                        // 点击查看大图
                        const modal = document.createElement('div');
                        modal.style.position = 'fixed';
                        modal.style.top = '0';
                        modal.style.left = '0';
                        modal.style.width = '100%';
                        modal.style.height = '100%';
                        modal.style.backgroundColor = 'rgba(0,0,0,0.9)';
                        modal.style.display = 'flex';
                        modal.style.alignItems = 'center';
                        modal.style.justifyContent = 'center';
                        modal.style.zIndex = '9999';

                        const modalImg = document.createElement('img');
                        modalImg.src = product.image;
                        modalImg.style.maxWidth = '90%';
                        modalImg.style.maxHeight = '90%';
                        modalImg.style.objectFit = 'contain';

                        modal.appendChild(modalImg);

                        // 点击关闭
                        modal.addEventListener('click', () => {
                            document.body.removeChild(modal);
                        });

                        document.body.appendChild(modal);
                    });

                    allProductsGrid.appendChild(productElement);
                });
            } catch (err) {
                console.error('移动端搜索产品时出错:', err);
            }
        } else {
            // PC端使用原有的加载逻辑
            displayAllProducts(filteredProducts);
        }
    }

    document.getElementById('closeDetail').addEventListener('click', closeProductDetail);

    // 语言数据补充
    if (typeof languages !== 'undefined') {
        // 中文翻译
        languages.zh['filter_rigid_busbar'] = '刚性母线';
        languages.zh['filter_laminated_busbar'] = '叠层母排';
        languages.zh['filter_motherboard'] = '导电排母线板';
        languages.zh['filter_flexible_busbar'] = '柔性母线';
        languages.zh['filter_flexible_circuit'] = '柔性电路刺破压接';
        languages.zh['filter_collector_busbar'] = '汇流排';
        languages.zh['filter_aluminum_busbar'] = '铝基导电排';
        languages.zh['filter_nickel_busbar'] = '镍基导电排';

        // 英文翻译
        languages.en['filter_rigid_busbar'] = 'Rigid Busbar';
        languages.en['filter_laminated_busbar'] = 'Laminated Busbar';
        languages.en['filter_motherboard'] = 'Busbar injection/thermoforming plastic motherboard';
        languages.en['filter_flexible_busbar'] = 'Flexible Busbar';
        languages.en['filter_flexible_circuit'] = 'FFC/FPC Crimping';
        languages.en['filter_collector_busbar'] = 'Multi-busbar System';
        languages.en['filter_aluminum_busbar'] = 'Aluminum Based Busbar';
        languages.en['filter_nickel_busbar'] = 'Nickel Based Busbar';
    }
});